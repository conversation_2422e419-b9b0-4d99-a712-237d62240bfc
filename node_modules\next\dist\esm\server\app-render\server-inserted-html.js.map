{"version": 3, "sources": ["../../../src/server/app-render/server-inserted-html.tsx"], "names": ["React", "ServerInsertedHTMLContext", "createServerInsertedHTML", "serverInsertedHTMLCallbacks", "addInsertedHtml", "handler", "push", "ServerInsertedHTMLProvider", "children", "Provider", "value", "renderServerInsertedHTML", "map", "callback", "index", "Fragment", "key"], "mappings": "AAAA,+EAA+E;AAC/E,iCAAiC;AAEjC,OAAOA,WAAW,QAAO;AACzB,SAASC,yBAAyB,QAAQ,uDAAsD;AAEhG,OAAO,SAASC;IACd,MAAMC,8BAAyD,EAAE;IACjE,MAAMC,kBAAkB,CAACC;QACvBF,4BAA4BG,IAAI,CAACD;IACnC;IAEA,OAAO;QACLE,4BAA2B,EAAEC,QAAQ,EAA6B;YAChE,qBACE,oBAACP,0BAA0BQ,QAAQ;gBAACC,OAAON;eACxCI;QAGP;QACAG;YACE,OAAOR,4BAA4BS,GAAG,CAAC,CAACC,UAAUC,sBAChD,oBAACd,MAAMe,QAAQ;oBAACC,KAAK,6BAA6BF;mBAC/CD;QAGP;IACF;AACF"}