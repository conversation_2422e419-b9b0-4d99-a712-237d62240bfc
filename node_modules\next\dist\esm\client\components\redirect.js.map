{"version": 3, "sources": ["../../../src/client/components/redirect.ts"], "names": ["requestAsyncStorage", "actionAsyncStorage", "RedirectStatusCode", "REDIRECT_ERROR_CODE", "RedirectType", "push", "replace", "getRedirectError", "url", "type", "statusCode", "TemporaryRedirect", "error", "Error", "digest", "requestStore", "getStore", "mutableCookies", "redirect", "actionStore", "isAction", "<PERSON><PERSON><PERSON>", "permanentRedirect", "PermanentRedirect", "isRedirectError", "errorCode", "destination", "status", "split", "Number", "isNaN", "getURLFromRedirectError", "getRedirectTypeFromError", "getRedirectStatusCodeFromError"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,mCAAkC;AAEtE,SAASC,kBAAkB,QAAQ,kCAAiC;AACpE,SAASC,kBAAkB,QAAQ,yBAAwB;AAE3D,MAAMC,sBAAsB;WAErB;UAAKC,YAAY;IAAZA,aACVC,UAAAA;IADUD,aAEVE,aAAAA;GAFUF,iBAAAA;AAUZ,OAAO,SAASG,iBACdC,GAAW,EACXC,IAAkB,EAClBC,UAAqE;IAArEA,IAAAA,uBAAAA,aAAiCR,mBAAmBS,iBAAiB;IAErE,MAAMC,QAAQ,IAAIC,MAAMV;IACxBS,MAAME,MAAM,GAAG,AAAGX,sBAAoB,MAAGM,OAAK,MAAGD,MAAI,MAAGE,aAAW;IACnE,MAAMK,eAAef,oBAAoBgB,QAAQ;IACjD,IAAID,cAAc;QAChBH,MAAMK,cAAc,GAAGF,aAAaE,cAAc;IACpD;IACA,OAAOL;AACT;AAEA;;;;;;CAMC,GACD,OAAO,SAASM,SACdV,GAAW,EACXC,IAAyC;IAAzCA,IAAAA,iBAAAA,OA/BU;IAiCV,MAAMU,cAAclB,mBAAmBe,QAAQ;IAC/C,MAAMT,iBACJC,KACAC,MACA,uDAAuD;IACvD,4DAA4D;IAC5D,kDAAkD;IAClDU,CAAAA,+BAAAA,YAAaC,QAAQ,IACjBlB,mBAAmBmB,QAAQ,GAC3BnB,mBAAmBS,iBAAiB;AAE5C;AAEA;;;;;;CAMC,GACD,OAAO,SAASW,kBACdd,GAAW,EACXC,IAAyC;IAAzCA,IAAAA,iBAAAA,OAvDU;IAyDV,MAAMU,cAAclB,mBAAmBe,QAAQ;IAC/C,MAAMT,iBACJC,KACAC,MACA,uDAAuD;IACvD,4DAA4D;IAC5D,kDAAkD;IAClDU,CAAAA,+BAAAA,YAAaC,QAAQ,IACjBlB,mBAAmBmB,QAAQ,GAC3BnB,mBAAmBqB,iBAAiB;AAE5C;AAEA;;;;;;CAMC,GACD,OAAO,SAASC,gBACdZ,KAAU;IAEV,IAAI,QAAOA,yBAAAA,MAAOE,MAAM,MAAK,UAAU,OAAO;IAE9C,MAAM,CAACW,WAAWhB,MAAMiB,aAAaC,OAAO,GAAG,AAACf,MAAME,MAAM,CAAYc,KAAK,CAC3E,KACA;IAGF,MAAMlB,aAAamB,OAAOF;IAE1B,OACEF,cAActB,uBACbM,CAAAA,SAAS,aAAaA,SAAS,MAAK,KACrC,OAAOiB,gBAAgB,YACvB,CAACI,MAAMpB,eACPA,cAAcR;AAElB;AAYA,OAAO,SAAS6B,wBAAwBnB,KAAU;IAChD,IAAI,CAACY,gBAAgBZ,QAAQ,OAAO;IAEpC,wEAAwE;IACxE,kBAAkB;IAClB,OAAOA,MAAME,MAAM,CAACc,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;AACtC;AAEA,OAAO,SAASI,yBACdpB,KAAuB;IAEvB,IAAI,CAACY,gBAAgBZ,QAAQ;QAC3B,MAAM,IAAIC,MAAM;IAClB;IAEA,OAAOD,MAAME,MAAM,CAACc,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;AACtC;AAEA,OAAO,SAASK,+BACdrB,KAAuB;IAEvB,IAAI,CAACY,gBAAgBZ,QAAQ;QAC3B,MAAM,IAAIC,MAAM;IAClB;IAEA,OAAOgB,OAAOjB,MAAME,MAAM,CAACc,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;AAC7C"}