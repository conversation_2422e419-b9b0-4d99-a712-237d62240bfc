// AI聊天管理器
import { Message } from '@/types'
import { createTTSManager, createSTTManager, TTSManager, STTManager } from '@/utils/speech'

/**
 * AI聊天响应
 */
export interface AIChatResponse {
  response: string
  emotion: string
  timestamp: string
  audioUrl?: string
  usage?: any
}

/**
 * AI聊天配置
 */
export interface AIChatConfig {
  enableTTS: boolean
  enableSTT: boolean
  autoSpeak: boolean
  voice: string
  speechRate: number
  speechPitch: number
  speechVolume: number
}

/**
 * AI聊天事件
 */
export interface AIChatEvents {
  onMessageSent?: (message: Message) => void
  onMessageReceived?: (message: Message) => void
  onSpeechStart?: () => void
  onSpeechEnd?: () => void
  onListeningStart?: () => void
  onListeningEnd?: () => void
  onError?: (error: Error) => void
}

/**
 * AI聊天管理器
 */
export class AIChatManager {
  private config: AIChatConfig
  private events: AIChatEvents
  private conversationHistory: Message[] = []
  private ttsManager: TTSManager | null = null
  private sttManager: STTManager | null = null
  private isInitialized: boolean = false
  private isSpeaking: boolean = false
  private isListening: boolean = false

  constructor(config: AIChatConfig, events: AIChatEvents = {}) {
    this.config = config
    this.events = events
  }

  /**
   * 初始化AI聊天管理器
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return

    try {
      // 初始化TTS
      if (this.config.enableTTS) {
        this.ttsManager = createTTSManager({
          voice: this.config.voice,
          rate: this.config.speechRate,
          pitch: this.config.speechPitch,
          volume: this.config.speechVolume
        })
      }

      // 初始化STT
      if (this.config.enableSTT) {
        this.sttManager = createSTTManager({
          language: 'zh-CN',
          continuous: false,
          interimResults: true
        })
      }

      this.isInitialized = true
      console.log('AI Chat Manager initialized')
    } catch (error) {
      console.error('Failed to initialize AI Chat Manager:', error)
      this.events.onError?.(error as Error)
      throw error
    }
  }

  /**
   * 发送消息
   */
  async sendMessage(content: string): Promise<Message> {
    if (!this.isInitialized) {
      throw new Error('AI Chat Manager not initialized')
    }

    try {
      // 创建用户消息
      const userMessage: Message = {
        id: Date.now().toString(),
        type: 'user',
        content,
        timestamp: new Date()
      }

      // 添加到历史记录
      this.conversationHistory.push(userMessage)
      this.events.onMessageSent?.(userMessage)

      // 发送到AI API
      const response = await this.callAIAPI(content)

      // 创建AI响应消息
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: response.response,
        timestamp: new Date(),
        audioUrl: response.audioUrl
      }

      // 添加到历史记录
      this.conversationHistory.push(aiMessage)
      this.events.onMessageReceived?.(aiMessage)

      // 自动语音播放
      if (this.config.autoSpeak && this.config.enableTTS) {
        await this.speak(response.response)
      }

      return aiMessage
    } catch (error) {
      console.error('Failed to send message:', error)
      this.events.onError?.(error as Error)
      throw error
    }
  }

  /**
   * 调用AI API
   */
  private async callAIAPI(message: string): Promise<AIChatResponse> {
    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          message,
          conversationHistory: this.conversationHistory.slice(-10)
        })
      })

      if (!response.ok) {
        throw new Error(`API request failed: ${response.statusText}`)
      }

      return await response.json()
    } catch (error) {
      console.error('AI API call failed:', error)
      throw error
    }
  }

  /**
   * 语音合成
   */
  async speak(text: string): Promise<void> {
    if (!this.ttsManager || this.isSpeaking) return

    try {
      this.isSpeaking = true
      this.events.onSpeechStart?.()

      // 首先尝试使用服务器端TTS
      const ttsResponse = await fetch('/api/tts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          text,
          voice: this.config.voice,
          rate: this.config.speechRate,
          pitch: this.config.speechPitch
        })
      })

      const ttsData = await ttsResponse.json()

      if (ttsData.audioUrl) {
        // 播放服务器生成的音频
        await this.playAudio(ttsData.audioUrl)
      } else {
        // 使用浏览器TTS
        await this.ttsManager.speak(text)
      }

    } catch (error) {
      console.error('Speech synthesis failed:', error)
      // 降级到浏览器TTS
      if (this.ttsManager) {
        await this.ttsManager.speak(text)
      }
    } finally {
      this.isSpeaking = false
      this.events.onSpeechEnd?.()
    }
  }

  /**
   * 播放音频文件
   */
  private async playAudio(audioUrl: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const audio = new Audio(audioUrl)
      
      audio.onended = () => resolve()
      audio.onerror = () => reject(new Error('Audio playback failed'))
      
      audio.play().catch(reject)
    })
  }

  /**
   * 开始语音识别
   */
  async startListening(): Promise<string> {
    if (!this.sttManager || this.isListening) {
      throw new Error('STT not available or already listening')
    }

    try {
      this.isListening = true
      this.events.onListeningStart?.()

      const result = await this.sttManager.startListening()
      
      return result
    } catch (error) {
      console.error('Speech recognition failed:', error)
      throw error
    } finally {
      this.isListening = false
      this.events.onListeningEnd?.()
    }
  }

  /**
   * 停止语音识别
   */
  stopListening(): void {
    if (this.sttManager && this.isListening) {
      this.sttManager.stopListening()
      this.isListening = false
      this.events.onListeningEnd?.()
    }
  }

  /**
   * 停止语音播放
   */
  stopSpeaking(): void {
    if (this.ttsManager && this.isSpeaking) {
      this.ttsManager.stop()
      this.isSpeaking = false
      this.events.onSpeechEnd?.()
    }
  }

  /**
   * 获取对话历史
   */
  getConversationHistory(): Message[] {
    return [...this.conversationHistory]
  }

  /**
   * 清除对话历史
   */
  clearHistory(): void {
    this.conversationHistory = []
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<AIChatConfig>): void {
    this.config = { ...this.config, ...newConfig }

    // 更新TTS配置
    if (this.ttsManager) {
      this.ttsManager.updateConfig({
        voice: this.config.voice,
        rate: this.config.speechRate,
        pitch: this.config.speechPitch,
        volume: this.config.speechVolume
      })
    }
  }

  /**
   * 更新事件处理器
   */
  updateEvents(newEvents: Partial<AIChatEvents>): void {
    this.events = { ...this.events, ...newEvents }
  }

  /**
   * 检查状态
   */
  getStatus(): {
    isInitialized: boolean
    isSpeaking: boolean
    isListening: boolean
    historyCount: number
  } {
    return {
      isInitialized: this.isInitialized,
      isSpeaking: this.isSpeaking,
      isListening: this.isListening,
      historyCount: this.conversationHistory.length
    }
  }

  /**
   * 释放资源
   */
  dispose(): void {
    this.stopSpeaking()
    this.stopListening()

    if (this.ttsManager) {
      this.ttsManager = null
    }

    if (this.sttManager) {
      this.sttManager = null
    }

    this.conversationHistory = []
    this.isInitialized = false
    
    console.log('AI Chat Manager disposed')
  }
}

/**
 * 默认配置
 */
export const DEFAULT_AI_CHAT_CONFIG: AIChatConfig = {
  enableTTS: true,
  enableSTT: true,
  autoSpeak: true,
  voice: 'zh-CN-XiaoxiaoNeural',
  speechRate: 1.0,
  speechPitch: 1.0,
  speechVolume: 0.8
}

/**
 * 创建AI聊天管理器
 */
export const createAIChatManager = (
  config: AIChatConfig = DEFAULT_AI_CHAT_CONFIG,
  events: AIChatEvents = {}
) => {
  return new AIChatManager(config, events)
}
