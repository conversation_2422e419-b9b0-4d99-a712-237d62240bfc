{"version": 3, "sources": ["../../../../src/server/future/route-matcher-providers/pages-route-matcher-provider.ts"], "names": ["isAPIRoute", "BLOCKED_PAGES", "PAGES_MANIFEST", "RouteKind", "PagesLocaleRouteMatcher", "PagesRouteMatcher", "ManifestRouteMatcherProvider", "PagesNormalizers", "PagesRouteMatcherProvider", "constructor", "distDir", "manifest<PERSON><PERSON>der", "i18nProvider", "normalizers", "transform", "manifest", "pathnames", "Object", "keys", "filter", "pathname", "normalized", "analyze", "includes", "matchers", "page", "detectedLocale", "push", "kind", "PAGES", "bundlePath", "normalize", "filename", "i18n", "locale"], "mappings": "AAAA,SAASA,UAAU,QAAQ,4BAA2B;AACtD,SAASC,aAAa,EAAEC,cAAc,QAAQ,gCAA+B;AAC7E,SAASC,SAAS,QAAQ,gBAAe;AACzC,SACEC,uBAAuB,EACvBC,iBAAiB,QACZ,wCAAuC;AAK9C,SAASC,4BAA4B,QAAQ,oCAAmC;AAEhF,SAASC,gBAAgB,QAAQ,6BAA4B;AAE7D,OAAO,MAAMC,kCAAkCF;IAG7CG,YACEC,OAAe,EACfC,cAA8B,EACbC,aACjB;QACA,KAAK,CAACV,gBAAgBS;4BAFLC;QAIjB,IAAI,CAACC,WAAW,GAAG,IAAIN,iBAAiBG;IAC1C;IAEA,MAAgBI,UACdC,QAAkB,EACyB;QAC3C,wEAAwE;QACxE,6BAA6B;QAC7B,MAAMC,YAAYC,OAAOC,IAAI,CAACH,UAC3BI,MAAM,CAAC,CAACC,WAAa,CAACpB,WAAWoB,UAClC,wEAAwE;QACxE,mBAAmB;SAClBD,MAAM,CAAC,CAACC;gBAEL;YADF,MAAMC,aACJ,EAAA,qBAAA,IAAI,CAACT,YAAY,qBAAjB,mBAAmBU,OAAO,CAACF,UAAUA,QAAQ,KAAIA;YAEnD,0BAA0B;YAC1B,IAAInB,cAAcsB,QAAQ,CAACF,aAAa,OAAO;YAE/C,OAAO;QACT;QAEF,MAAMG,WAAqC,EAAE;QAC7C,KAAK,MAAMC,QAAQT,UAAW;YAC5B,IAAI,IAAI,CAACJ,YAAY,EAAE;gBACrB,uEAAuE;gBACvE,MAAM,EAAEc,cAAc,EAAEN,QAAQ,EAAE,GAAG,IAAI,CAACR,YAAY,CAACU,OAAO,CAACG;gBAE/DD,SAASG,IAAI,CACX,IAAIvB,wBAAwB;oBAC1BwB,MAAMzB,UAAU0B,KAAK;oBACrBT;oBACAK;oBACAK,YAAY,IAAI,CAACjB,WAAW,CAACiB,UAAU,CAACC,SAAS,CAACN;oBAClDO,UAAU,IAAI,CAACnB,WAAW,CAACmB,QAAQ,CAACD,SAAS,CAAChB,QAAQ,CAACU,KAAK;oBAC5DQ,MAAM;wBACJC,QAAQR;oBACV;gBACF;YAEJ,OAAO;gBACLF,SAASG,IAAI,CACX,IAAItB,kBAAkB;oBACpBuB,MAAMzB,UAAU0B,KAAK;oBACrB,qDAAqD;oBACrDT,UAAUK;oBACVA;oBACAK,YAAY,IAAI,CAACjB,WAAW,CAACiB,UAAU,CAACC,SAAS,CAACN;oBAClDO,UAAU,IAAI,CAACnB,WAAW,CAACmB,QAAQ,CAACD,SAAS,CAAChB,QAAQ,CAACU,KAAK;gBAC9D;YAEJ;QACF;QAEA,OAAOD;IACT;AACF"}