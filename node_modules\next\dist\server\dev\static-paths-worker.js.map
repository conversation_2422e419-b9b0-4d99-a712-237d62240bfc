{"version": 3, "sources": ["../../../src/server/dev/static-paths-worker.ts"], "names": ["loadStaticPaths", "dir", "distDir", "pathname", "config", "httpAgentOptions", "locales", "defaultLocale", "isAppPath", "page", "isrFlushToDisk", "fetchCacheKeyPrefix", "maxMemoryCacheSize", "requestHeaders", "incremental<PERSON>ache<PERSON>andlerPath", "ppr", "require", "setConfig", "setHttpClientAndAgentOptions", "components", "loadComponents", "getStaticPaths", "Error", "routeModule", "generateParams", "isAppRouteRouteModule", "revalidate", "userland", "dynamic", "dynamicParams", "generateStaticParams", "segmentPath", "collectGenerateParams", "ComponentMod", "tree", "buildAppStaticPaths", "configFileName", "buildStaticPaths"], "mappings": ";;;;+BAyBsBA;;;eAAAA;;;QAvBf;QACA;uBAMA;gCAEwB;mCACc;wBAEP;AAW/B,eAAeA,gBAAgB,EACpCC,GAAG,EACHC,OAAO,EACPC,QAAQ,EACRC,MAAM,EACNC,gBAAgB,EAChBC,OAAO,EACPC,aAAa,EACbC,SAAS,EACTC,IAAI,EACJC,cAAc,EACdC,mBAAmB,EACnBC,kBAAkB,EAClBC,cAAc,EACdC,2BAA2B,EAC3BC,GAAG,EAiBJ;IAKC,oCAAoC;IACpCC,QAAQ,4CAA4CC,SAAS,CAACb;IAC9Dc,IAAAA,+CAA4B,EAAC;QAC3Bb;IACF;IAEA,MAAMc,aAAa,MAAMC,IAAAA,8BAAc,EAAC;QACtClB;QACA,qDAAqD;QACrDO,MAAMA,QAAQN;QACdK;IACF;IAEA,IAAI,CAACW,WAAWE,cAAc,IAAI,CAACb,WAAW;QAC5C,yDAAyD;QACzD,mDAAmD;QACnD,MAAM,IAAIc,MACR,CAAC,uDAAuD,EAAEnB,SAAS,CAAC;IAExE;IAEA,IAAIK,WAAW;QACb,MAAM,EAAEe,WAAW,EAAE,GAAGJ;QACxB,MAAMK,iBACJD,eAAeE,IAAAA,6BAAqB,EAACF,eACjC;YACE;gBACEnB,QAAQ;oBACNsB,YAAYH,YAAYI,QAAQ,CAACD,UAAU;oBAC3CE,SAASL,YAAYI,QAAQ,CAACC,OAAO;oBACrCC,eAAeN,YAAYI,QAAQ,CAACE,aAAa;gBACnD;gBACAC,sBAAsBP,YAAYI,QAAQ,CAACG,oBAAoB;gBAC/DC,aAAa5B;YACf;SACD,GACD,MAAM6B,IAAAA,4BAAqB,EAACb,WAAWc,YAAY,CAACC,IAAI;QAE9D,OAAO,MAAMC,IAAAA,0BAAmB,EAAC;YAC/BlC;YACAQ,MAAMN;YACNqB;YACAY,gBAAgBhC,OAAOgC,cAAc;YACrClC;YACAW;YACAC;YACAJ;YACAC;YACAC;YACAG;YACAkB,cAAcd,WAAWc,YAAY;QACvC;IACF;IAEA,OAAO,MAAMI,IAAAA,uBAAgB,EAAC;QAC5B5B,MAAMN;QACNkB,gBAAgBF,WAAWE,cAAc;QACzCe,gBAAgBhC,OAAOgC,cAAc;QACrC9B;QACAC;IACF;AACF"}