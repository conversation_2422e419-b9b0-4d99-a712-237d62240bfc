{"version": 3, "sources": ["../../../src/lib/metadata/metadata.tsx"], "names": ["createMetadataComponents", "tree", "pathname", "searchParams", "getDynamicParamFromSegment", "appUsingSizeAdjustment", "errorType", "metadataContext", "resolve", "metadataErrorResolving", "Promise", "res", "MetadataTree", "defaultMetadata", "createDefaultMetadata", "defaultViewport", "createDefaultViewport", "metadata", "viewport", "error", "errorMetadataItem", "errorConvention", "undefined", "resolvedError", "resolvedMetadata", "resolvedViewport", "resolveMetadata", "parentParams", "metadataItems", "isNotFoundError", "notFoundMetadataError", "notFoundMetadata", "notFoundViewport", "elements", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ViewportMeta", "BasicMeta", "AlternatesMetadata", "alternates", "ItunesMeta", "itunes", "FormatDetectionMeta", "formatDetection", "VerificationMeta", "verification", "AppleWebAppMeta", "appleWebApp", "OpenGraphMetadata", "openGraph", "TwitterMetadata", "twitter", "AppLinksMeta", "appLinks", "IconsMetadata", "icons", "push", "meta", "name", "map", "el", "index", "React", "cloneElement", "key", "MetadataOutlet"], "mappings": ";;;;+BAqCgBA;;;eAAAA;;;8DAlCE;uBAQX;2BAC4B;2BAK5B;uBACuB;iCACE;sBACL;iCAQpB;0BACyB;;;;;;AAQzB,SAASA,yBAAyB,EACvCC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,0BAA0B,EAC1BC,sBAAsB,EACtBC,SAAS,EAQV;IACC,MAAMC,kBAAkB;QACtBL;IACF;IAEA,IAAIM;IACJ,8DAA8D;IAC9D,MAAMC,yBAAyB,IAAIC,QAA2B,CAACC;QAC7DH,UAAUG;IACZ;IAEA,eAAeC;QACb,MAAMC,kBAAkBC,IAAAA,sCAAqB;QAC7C,MAAMC,kBAAkBC,IAAAA,sCAAqB;QAC7C,IAAIC,WAAyCJ;QAC7C,IAAIK,WAAyCH;QAC7C,IAAII;QACJ,MAAMC,oBAAwC;YAAC;YAAM;YAAM;SAAK;QAChE,MAAMC,kBAAkBf,cAAc,aAAagB,YAAYhB;QAE/D,MAAM,CAACiB,eAAeC,kBAAkBC,iBAAiB,GACvD,MAAMC,IAAAA,gCAAe,EAAC;YACpBzB;YACA0B,cAAc,CAAC;YACfC,eAAe,EAAE;YACjBR;YACAjB;YACAC;YACAiB;YACAd;QACF;QACF,IAAI,CAACgB,eAAe;YAClBL,WAAWO;YACXR,WAAWO;YACXhB,QAAQc;QACV,OAAO;YACLH,QAAQI;YACR,0FAA0F;YAC1F,kGAAkG;YAClG,kDAAkD;YAClD,IAAI,CAACjB,aAAauB,IAAAA,yBAAe,EAACN,gBAAgB;gBAChD,MAAM,CAACO,uBAAuBC,kBAAkBC,iBAAiB,GAC/D,MAAMN,IAAAA,gCAAe,EAAC;oBACpBzB;oBACA0B,cAAc,CAAC;oBACfC,eAAe,EAAE;oBACjBR;oBACAjB;oBACAC;oBACAiB,iBAAiB;oBACjBd;gBACF;gBACFW,WAAWc;gBACXf,WAAWc;gBACXZ,QAAQW,yBAAyBX;YACnC;YACAX,QAAQW;QACV;QAEA,MAAMc,WAAWC,IAAAA,gBAAU,EAAC;YAC1BC,IAAAA,mBAAY,EAAC;gBAAEjB,UAAUA;YAAS;YAClCkB,IAAAA,gBAAS,EAAC;gBAAEnB;YAAS;YACrBoB,IAAAA,6BAAkB,EAAC;gBAAEC,YAAYrB,SAASqB,UAAU;YAAC;YACrDC,IAAAA,iBAAU,EAAC;gBAAEC,QAAQvB,SAASuB,MAAM;YAAC;YACrCC,IAAAA,0BAAmB,EAAC;gBAAEC,iBAAiBzB,SAASyB,eAAe;YAAC;YAChEC,IAAAA,uBAAgB,EAAC;gBAAEC,cAAc3B,SAAS2B,YAAY;YAAC;YACvDC,IAAAA,sBAAe,EAAC;gBAAEC,aAAa7B,SAAS6B,WAAW;YAAC;YACpDC,IAAAA,4BAAiB,EAAC;gBAAEC,WAAW/B,SAAS+B,SAAS;YAAC;YAClDC,IAAAA,0BAAe,EAAC;gBAAEC,SAASjC,SAASiC,OAAO;YAAC;YAC5CC,IAAAA,uBAAY,EAAC;gBAAEC,UAAUnC,SAASmC,QAAQ;YAAC;YAC3CC,IAAAA,oBAAa,EAAC;gBAAEC,OAAOrC,SAASqC,KAAK;YAAC;SACvC;QAED,IAAIjD,wBAAwB4B,SAASsB,IAAI,eAAC,6BAACC;YAAKC,MAAK;;QAErD,qBACE,4DACGxB,SAASyB,GAAG,CAAC,CAACC,IAAIC;YACjB,qBAAOC,cAAK,CAACC,YAAY,CAACH,IAA0B;gBAAEI,KAAKH;YAAM;QACnE;IAGN;IAEA,eAAeI;QACb,MAAM7C,QAAQ,MAAMV;QACpB,IAAIU,OAAO;YACT,MAAMA;QACR;QACA,OAAO;IACT;IAEA,OAAO;QAACP;QAAcoD;KAAe;AACvC"}