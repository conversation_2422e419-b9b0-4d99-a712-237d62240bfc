// 视频聊天管理器
import { WebRTCManager, createWebRTCManager, DEFAULT_WEBRTC_CONFIG } from './webrtc-manager'
import { SignalingClient, createSignalingClient, generateUserId, generateRoomId } from './signaling-client'
import { FaceData } from '@/types'

/**
 * 视频聊天事件
 */
export interface VideoChatEvents {
  onLocalStream?: (stream: MediaStream) => void
  onRemoteStream?: (stream: MediaStream) => void
  onConnectionStateChange?: (state: string) => void
  onFaceData?: (faceData: FaceData) => void
  onError?: (error: Error) => void
}

/**
 * 视频聊天管理器
 */
export class VideoChatManager {
  private webrtcManager: WebRTCManager
  private signalingClient: SignalingClient
  private userId: string
  private roomId: string | null = null
  private isInitialized: boolean = false
  private events: VideoChatEvents = {}

  constructor(userId?: string) {
    this.userId = userId || generateUserId()
    this.webrtcManager = createWebRTCManager(DEFAULT_WEBRTC_CONFIG)
    this.signalingClient = createSignalingClient(this.userId)
  }

  /**
   * 初始化视频聊天
   */
  async initialize(events: VideoChatEvents = {}): Promise<void> {
    if (this.isInitialized) return

    try {
      this.events = events

      // 初始化WebRTC
      await this.webrtcManager.initialize()

      // 设置WebRTC事件监听器
      this.setupWebRTCEventListeners()

      // 连接信令服务器
      await this.signalingClient.connect()

      // 设置信令事件监听器
      this.setupSignalingEventListeners()

      this.isInitialized = true
      console.log('Video chat manager initialized')
    } catch (error) {
      console.error('Failed to initialize video chat manager:', error)
      this.events.onError?.(error as Error)
      throw error
    }
  }

  /**
   * 设置WebRTC事件监听器
   */
  private setupWebRTCEventListeners(): void {
    this.webrtcManager.setOnRemoteStream((stream) => {
      console.log('Received remote stream')
      this.events.onRemoteStream?.(stream)
    })

    this.webrtcManager.setOnConnectionState((state) => {
      console.log('WebRTC connection state:', state)
      this.events.onConnectionStateChange?.(state)
    })
  }

  /**
   * 设置信令事件监听器
   */
  private setupSignalingEventListeners(): void {
    // 处理offer
    this.signalingClient.onMessage('offer', async (message) => {
      try {
        console.log('Received offer')
        const answer = await this.webrtcManager.createAnswer(message.data)
        
        this.signalingClient.sendMessage({
          type: 'answer',
          data: answer,
          to: message.from
        })
      } catch (error) {
        console.error('Failed to handle offer:', error)
        this.events.onError?.(error as Error)
      }
    })

    // 处理answer
    this.signalingClient.onMessage('answer', async (message) => {
      try {
        console.log('Received answer')
        await this.webrtcManager.setRemoteDescription(message.data)
      } catch (error) {
        console.error('Failed to handle answer:', error)
        this.events.onError?.(error as Error)
      }
    })

    // 处理ICE候选
    this.signalingClient.onMessage('ice-candidate', async (message) => {
      try {
        console.log('Received ICE candidate')
        await this.webrtcManager.addIceCandidate(message.data)
      } catch (error) {
        console.error('Failed to handle ICE candidate:', error)
        this.events.onError?.(error as Error)
      }
    })

    // 处理用户加入
    this.signalingClient.onMessage('user-joined', (message) => {
      console.log('User joined:', message.data)
      // 如果是新用户加入，主动发起连接
      if (message.data.userId !== this.userId) {
        this.initiateCall()
      }
    })

    // 处理用户离开
    this.signalingClient.onMessage('user-left', (message) => {
      console.log('User left:', message.data)
      // 可以在这里处理用户离开的逻辑
    })
  }

  /**
   * 开始本地视频
   */
  async startLocalVideo(): Promise<MediaStream> {
    if (!this.isInitialized) {
      throw new Error('Video chat manager not initialized')
    }

    try {
      const stream = await this.webrtcManager.getLocalStream()
      this.webrtcManager.addLocalStream(stream)
      
      console.log('Local video started')
      this.events.onLocalStream?.(stream)
      
      return stream
    } catch (error) {
      console.error('Failed to start local video:', error)
      this.events.onError?.(error as Error)
      throw error
    }
  }

  /**
   * 加入房间
   */
  async joinRoom(roomId?: string): Promise<string> {
    if (!this.isInitialized) {
      throw new Error('Video chat manager not initialized')
    }

    try {
      this.roomId = roomId || generateRoomId()
      await this.signalingClient.joinRoom(this.roomId)
      
      console.log(`Joined room: ${this.roomId}`)
      return this.roomId
    } catch (error) {
      console.error('Failed to join room:', error)
      this.events.onError?.(error as Error)
      throw error
    }
  }

  /**
   * 离开房间
   */
  async leaveRoom(): Promise<void> {
    if (!this.roomId) return

    try {
      await this.signalingClient.leaveRoom()
      this.webrtcManager.close()
      
      console.log('Left room')
      this.roomId = null
    } catch (error) {
      console.error('Failed to leave room:', error)
      this.events.onError?.(error as Error)
    }
  }

  /**
   * 发起通话
   */
  async initiateCall(): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('Video chat manager not initialized')
    }

    try {
      console.log('Initiating call...')
      const offer = await this.webrtcManager.createOffer()
      
      this.signalingClient.sendMessage({
        type: 'offer',
        data: offer
      })
      
      console.log('Call initiated')
    } catch (error) {
      console.error('Failed to initiate call:', error)
      this.events.onError?.(error as Error)
      throw error
    }
  }

  /**
   * 获取连接状态
   */
  getConnectionState(): string {
    return this.webrtcManager.getConnectionState()
  }

  /**
   * 检查是否已连接
   */
  isConnected(): boolean {
    return this.webrtcManager.isConnectionActive()
  }

  /**
   * 获取本地流
   */
  getLocalStream(): MediaStream | null {
    return this.webrtcManager.getLocalStreamInstance()
  }

  /**
   * 获取远程流
   */
  getRemoteStream(): MediaStream | null {
    return this.webrtcManager.getRemoteStreamInstance()
  }

  /**
   * 获取用户ID
   */
  getUserId(): string {
    return this.userId
  }

  /**
   * 获取房间ID
   */
  getRoomId(): string | null {
    return this.roomId
  }

  /**
   * 更新事件处理器
   */
  updateEvents(events: Partial<VideoChatEvents>): void {
    this.events = { ...this.events, ...events }
  }

  /**
   * 关闭连接
   */
  close(): void {
    this.webrtcManager.close()
    this.signalingClient.disconnect()
    this.roomId = null
    console.log('Video chat manager closed')
  }

  /**
   * 释放资源
   */
  dispose(): void {
    this.close()
    this.webrtcManager.dispose()
    this.signalingClient.dispose()
    this.isInitialized = false
    this.events = {}
    console.log('Video chat manager disposed')
  }
}

/**
 * 创建视频聊天管理器
 */
export const createVideoChatManager = (userId?: string) => {
  return new VideoChatManager(userId)
}

/**
 * 视频聊天工具函数
 */
export class VideoChatUtils {
  /**
   * 检查浏览器支持
   */
  static checkBrowserSupport(): {
    webrtc: boolean
    getUserMedia: boolean
    websocket: boolean
  } {
    return {
      webrtc: !!(window.RTCPeerConnection || (window as any).webkitRTCPeerConnection),
      getUserMedia: !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia),
      websocket: !!window.WebSocket
    }
  }

  /**
   * 获取媒体约束建议
   */
  static getRecommendedConstraints(): MediaStreamConstraints {
    return {
      video: {
        width: { ideal: 640, max: 1280 },
        height: { ideal: 480, max: 720 },
        frameRate: { ideal: 30, max: 60 },
        facingMode: 'user'
      },
      audio: {
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true,
        sampleRate: 44100
      }
    }
  }

  /**
   * 测试网络连接
   */
  static async testNetworkConnection(): Promise<{
    latency: number
    bandwidth: number
    quality: 'excellent' | 'good' | 'fair' | 'poor'
  }> {
    // 简化的网络测试
    const startTime = Date.now()
    
    try {
      await fetch('https://www.google.com/favicon.ico', { mode: 'no-cors' })
      const latency = Date.now() - startTime
      
      // 简单的质量评估
      let quality: 'excellent' | 'good' | 'fair' | 'poor'
      if (latency < 50) quality = 'excellent'
      else if (latency < 100) quality = 'good'
      else if (latency < 200) quality = 'fair'
      else quality = 'poor'

      return {
        latency,
        bandwidth: 1000, // 模拟值
        quality
      }
    } catch (error) {
      return {
        latency: 999,
        bandwidth: 0,
        quality: 'poor'
      }
    }
  }
}
