{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/metadata/resolve-route-data.ts"], "names": ["resolveRobots", "resolveSitemap", "resolveManifest", "resolveRouteData", "data", "content", "rules", "Array", "isArray", "rule", "userAgent", "resolveArray", "agent", "allow", "item", "disallow", "crawlDelay", "host", "sitemap", "for<PERSON>ach", "url", "lastModified", "serializedDate", "Date", "toISOString", "changeFrequency", "priority", "JSON", "stringify", "fileType"], "mappings": ";;;;;;;;;;;;;;;;;IAIgBA,aAAa;eAAbA;;IAyCAC,cAAc;eAAdA;;IAkCAC,eAAe;eAAfA;;IAIAC,gBAAgB;eAAhBA;;;uBAlFa;AAGtB,SAASH,cAAcI,IAA0B;IACtD,IAAIC,UAAU;IACd,MAAMC,QAAQC,MAAMC,OAAO,CAACJ,KAAKE,KAAK,IAAIF,KAAKE,KAAK,GAAG;QAACF,KAAKE,KAAK;KAAC;IACnE,KAAK,MAAMG,QAAQH,MAAO;QACxB,MAAMI,YAAYC,IAAAA,mBAAY,EAACF,KAAKC,SAAS,IAAI;YAAC;SAAI;QACtD,KAAK,MAAME,SAASF,UAAW;YAC7BL,WAAW,CAAC,YAAY,EAAEO,MAAM,EAAE,CAAC;QACrC;QACA,IAAIH,KAAKI,KAAK,EAAE;YACd,MAAMA,QAAQF,IAAAA,mBAAY,EAACF,KAAKI,KAAK;YACrC,KAAK,MAAMC,QAAQD,MAAO;gBACxBR,WAAW,CAAC,OAAO,EAAES,KAAK,EAAE,CAAC;YAC/B;QACF;QACA,IAAIL,KAAKM,QAAQ,EAAE;YACjB,MAAMA,WAAWJ,IAAAA,mBAAY,EAACF,KAAKM,QAAQ;YAC3C,KAAK,MAAMD,QAAQC,SAAU;gBAC3BV,WAAW,CAAC,UAAU,EAAES,KAAK,EAAE,CAAC;YAClC;QACF;QACA,IAAIL,KAAKO,UAAU,EAAE;YACnBX,WAAW,CAAC,aAAa,EAAEI,KAAKO,UAAU,CAAC,EAAE,CAAC;QAChD;QACAX,WAAW;IACb;IACA,IAAID,KAAKa,IAAI,EAAE;QACbZ,WAAW,CAAC,MAAM,EAAED,KAAKa,IAAI,CAAC,EAAE,CAAC;IACnC;IACA,IAAIb,KAAKc,OAAO,EAAE;QAChB,MAAMA,UAAUP,IAAAA,mBAAY,EAACP,KAAKc,OAAO;QACzC,+DAA+D;QAC/DA,QAAQC,OAAO,CAAC,CAACL;YACfT,WAAW,CAAC,SAAS,EAAES,KAAK,EAAE,CAAC;QACjC;IACF;IAEA,OAAOT;AACT;AAIO,SAASJ,eAAeG,IAA2B;IACxD,IAAIC,UAAU;IACdA,WAAW;IACXA,WAAW;IAEX,KAAK,MAAMS,QAAQV,KAAM;QACvBC,WAAW;QACXA,WAAW,CAAC,KAAK,EAAES,KAAKM,GAAG,CAAC,QAAQ,CAAC;QAErC,IAAIN,KAAKO,YAAY,EAAE;YACrB,MAAMC,iBACJR,KAAKO,YAAY,YAAYE,OACzBT,KAAKO,YAAY,CAACG,WAAW,KAC7BV,KAAKO,YAAY;YAEvBhB,WAAW,CAAC,SAAS,EAAEiB,eAAe,YAAY,CAAC;QACrD;QAEA,IAAIR,KAAKW,eAAe,EAAE;YACxBpB,WAAW,CAAC,YAAY,EAAES,KAAKW,eAAe,CAAC,eAAe,CAAC;QACjE;QAEA,IAAI,OAAOX,KAAKY,QAAQ,KAAK,UAAU;YACrCrB,WAAW,CAAC,UAAU,EAAES,KAAKY,QAAQ,CAAC,aAAa,CAAC;QACtD;QAEArB,WAAW;IACb;IAEAA,WAAW;IAEX,OAAOA;AACT;AAEO,SAASH,gBAAgBE,IAA4B;IAC1D,OAAOuB,KAAKC,SAAS,CAACxB;AACxB;AAEO,SAASD,iBACdC,IAA2E,EAC3EyB,QAA2C;IAE3C,IAAIA,aAAa,UAAU;QACzB,OAAO7B,cAAcI;IACvB;IACA,IAAIyB,aAAa,WAAW;QAC1B,OAAO5B,eAAeG;IACxB;IACA,IAAIyB,aAAa,YAAY;QAC3B,OAAO3B,gBAAgBE;IACzB;IACA,OAAO;AACT"}