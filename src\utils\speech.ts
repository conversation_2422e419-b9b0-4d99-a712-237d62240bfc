// 语音处理工具函数
import { TTSConfig, STTConfig } from '@/types'

/**
 * 语音合成管理器 (Text-to-Speech)
 */
export class TTSManager {
  private synth: SpeechSynthesis
  private config: TTSConfig
  private currentUtterance: SpeechSynthesisUtterance | null = null

  constructor(config: TTSConfig) {
    this.synth = window.speechSynthesis
    this.config = config
  }

  /**
   * 获取可用的语音列表
   */
  getAvailableVoices(): SpeechSynthesisVoice[] {
    return this.synth.getVoices()
  }

  /**
   * 语音合成
   */
  speak(text: string): Promise<void> {
    return new Promise((resolve, reject) => {
      // 停止当前播放
      this.stop()

      const utterance = new SpeechSynthesisUtterance(text)
      
      // 设置语音参数
      const voices = this.getAvailableVoices()
      const selectedVoice = voices.find(voice => 
        voice.name.includes(this.config.voice) || 
        voice.lang.includes('zh-CN')
      )
      
      if (selectedVoice) {
        utterance.voice = selectedVoice
      }

      utterance.rate = this.config.rate
      utterance.pitch = this.config.pitch
      utterance.volume = this.config.volume

      // 设置事件监听器
      utterance.onstart = () => {
        console.log('TTS started')
      }

      utterance.onend = () => {
        console.log('TTS finished')
        this.currentUtterance = null
        resolve()
      }

      utterance.onerror = (event) => {
        console.error('TTS error:', event.error)
        this.currentUtterance = null
        reject(new Error(`TTS error: ${event.error}`))
      }

      // 开始语音合成
      this.currentUtterance = utterance
      this.synth.speak(utterance)
    })
  }

  /**
   * 停止语音合成
   */
  stop() {
    if (this.synth.speaking) {
      this.synth.cancel()
    }
    this.currentUtterance = null
  }

  /**
   * 暂停语音合成
   */
  pause() {
    if (this.synth.speaking) {
      this.synth.pause()
    }
  }

  /**
   * 恢复语音合成
   */
  resume() {
    if (this.synth.paused) {
      this.synth.resume()
    }
  }

  /**
   * 检查是否正在播放
   */
  isSpeaking(): boolean {
    return this.synth.speaking
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<TTSConfig>) {
    this.config = { ...this.config, ...newConfig }
  }
}

/**
 * 语音识别管理器 (Speech-to-Text)
 */
export class STTManager {
  private recognition: any = null
  private config: STTConfig
  private isListening: boolean = false

  constructor(config: STTConfig) {
    this.config = config
    this.initializeRecognition()
  }

  /**
   * 初始化语音识别
   */
  private initializeRecognition() {
    // 检查浏览器支持
    const SpeechRecognition = (window as any).SpeechRecognition || 
                             (window as any).webkitSpeechRecognition

    if (!SpeechRecognition) {
      console.warn('Speech recognition not supported in this browser')
      return
    }

    this.recognition = new SpeechRecognition()
    
    // 配置语音识别
    this.recognition.lang = this.config.language
    this.recognition.continuous = this.config.continuous
    this.recognition.interimResults = this.config.interimResults
  }

  /**
   * 开始语音识别
   */
  startListening(): Promise<string> {
    return new Promise((resolve, reject) => {
      if (!this.recognition) {
        reject(new Error('Speech recognition not supported'))
        return
      }

      if (this.isListening) {
        reject(new Error('Already listening'))
        return
      }

      let finalTranscript = ''

      // 设置事件监听器
      this.recognition.onstart = () => {
        console.log('STT started')
        this.isListening = true
      }

      this.recognition.onresult = (event: any) => {
        let interimTranscript = ''

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript
          
          if (event.results[i].isFinal) {
            finalTranscript += transcript
          } else {
            interimTranscript += transcript
          }
        }

        console.log('Interim transcript:', interimTranscript)
        console.log('Final transcript:', finalTranscript)
      }

      this.recognition.onend = () => {
        console.log('STT ended')
        this.isListening = false
        resolve(finalTranscript.trim())
      }

      this.recognition.onerror = (event: any) => {
        console.error('STT error:', event.error)
        this.isListening = false
        reject(new Error(`STT error: ${event.error}`))
      }

      // 开始识别
      this.recognition.start()
    })
  }

  /**
   * 停止语音识别
   */
  stopListening() {
    if (this.recognition && this.isListening) {
      this.recognition.stop()
    }
  }

  /**
   * 检查是否正在监听
   */
  isListeningActive(): boolean {
    return this.isListening
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<STTConfig>) {
    this.config = { ...this.config, ...newConfig }
    
    if (this.recognition) {
      this.recognition.lang = this.config.language
      this.recognition.continuous = this.config.continuous
      this.recognition.interimResults = this.config.interimResults
    }
  }
}

/**
 * 音频处理工具
 */
export class AudioProcessor {
  private audioContext: AudioContext | null = null
  private analyser: AnalyserNode | null = null
  private microphone: MediaStreamAudioSourceNode | null = null

  /**
   * 初始化音频上下文
   */
  async initialize(): Promise<void> {
    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
      this.analyser = this.audioContext.createAnalyser()
      this.analyser.fftSize = 256

      // 获取麦克风权限
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
      this.microphone = this.audioContext.createMediaStreamSource(stream)
      this.microphone.connect(this.analyser)

      console.log('Audio processor initialized')
    } catch (error) {
      console.error('Failed to initialize audio processor:', error)
      throw error
    }
  }

  /**
   * 获取音频频谱数据
   */
  getFrequencyData(): Uint8Array | null {
    if (!this.analyser) return null

    const bufferLength = this.analyser.frequencyBinCount
    const dataArray = new Uint8Array(bufferLength)
    this.analyser.getByteFrequencyData(dataArray)

    return dataArray
  }

  /**
   * 获取音量级别
   */
  getVolumeLevel(): number {
    const frequencyData = this.getFrequencyData()
    if (!frequencyData) return 0

    let sum = 0
    for (let i = 0; i < frequencyData.length; i++) {
      sum += frequencyData[i]
    }

    return sum / frequencyData.length / 255 // 归一化到0-1
  }

  /**
   * 检测是否有语音活动
   */
  detectVoiceActivity(threshold: number = 0.1): boolean {
    const volumeLevel = this.getVolumeLevel()
    return volumeLevel > threshold
  }

  /**
   * 释放资源
   */
  dispose() {
    if (this.microphone) {
      this.microphone.disconnect()
      this.microphone = null
    }

    if (this.audioContext) {
      this.audioContext.close()
      this.audioContext = null
    }

    this.analyser = null
  }
}

/**
 * 默认配置
 */
export const DEFAULT_TTS_CONFIG: TTSConfig = {
  voice: 'zh-CN',
  rate: 1.0,
  pitch: 1.0,
  volume: 0.8
}

export const DEFAULT_STT_CONFIG: STTConfig = {
  language: 'zh-CN',
  continuous: false,
  interimResults: true
}

/**
 * 创建语音管理器
 */
export const createTTSManager = (config: TTSConfig = DEFAULT_TTS_CONFIG) => {
  return new TTSManager(config)
}

export const createSTTManager = (config: STTConfig = DEFAULT_STT_CONFIG) => {
  return new STTManager(config)
}

export const createAudioProcessor = () => {
  return new AudioProcessor()
}
