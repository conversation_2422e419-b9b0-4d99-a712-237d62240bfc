// AI聊天工具函数
import { AIConfig, Message } from '@/types'

/**
 * AI聊天管理器
 */
export class AIChatManager {
  private config: AIConfig
  private conversationHistory: Message[] = []

  constructor(config: AIConfig) {
    this.config = config
  }

  /**
   * 发送消息到AI
   */
  async sendMessage(message: string): Promise<string> {
    try {
      // 添加用户消息到历史记录
      const userMessage: Message = {
        id: Date.now().toString(),
        type: 'user',
        content: message,
        timestamp: new Date()
      }
      this.conversationHistory.push(userMessage)

      // 构建请求数据
      const requestData = {
        model: this.config.model,
        messages: [
          {
            role: 'system',
            content: this.config.systemPrompt
          },
          ...this.conversationHistory.slice(-10).map(msg => ({
            role: msg.type === 'user' ? 'user' : 'assistant',
            content: msg.content
          }))
        ],
        temperature: this.config.temperature,
        max_tokens: this.config.maxTokens
      }

      // 发送请求到AI API
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.apiKey}`
        },
        body: JSON.stringify(requestData)
      })

      if (!response.ok) {
        throw new Error(`AI API request failed: ${response.statusText}`)
      }

      const data = await response.json()
      const aiResponse = data.choices[0].message.content

      // 添加AI响应到历史记录
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: aiResponse,
        timestamp: new Date()
      }
      this.conversationHistory.push(aiMessage)

      return aiResponse
    } catch (error) {
      console.error('Error sending message to AI:', error)
      throw error
    }
  }

  /**
   * 获取对话历史
   */
  getConversationHistory(): Message[] {
    return [...this.conversationHistory]
  }

  /**
   * 清除对话历史
   */
  clearHistory() {
    this.conversationHistory = []
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<AIConfig>) {
    this.config = { ...this.config, ...newConfig }
  }
}

/**
 * 创建AI聊天管理器
 */
export const createAIChatManager = (config: AIConfig) => {
  return new AIChatManager(config)
}

/**
 * 默认AI配置
 */
export const DEFAULT_AI_CONFIG: AIConfig = {
  apiKey: process.env.NEXT_PUBLIC_OPENAI_API_KEY || '',
  model: 'gpt-3.5-turbo',
  temperature: 0.7,
  maxTokens: 500,
  systemPrompt: `你是一个友好的数字人助手，名叫小铃。你的特点是：
1. 性格活泼开朗，喜欢与用户互动
2. 会根据对话内容表现出相应的情感
3. 回答简洁明了，不会过于冗长
4. 偶尔会使用一些可爱的表情符号
5. 能够理解用户的情感并给予适当的回应

请用中文回答，保持友好和专业的态度。`
}

/**
 * 情感分析工具
 */
export class EmotionAnalyzer {
  /**
   * 分析文本情感
   */
  static analyzeEmotion(text: string): {
    emotion: string
    confidence: number
  } {
    // 简单的情感分析实现
    const emotionKeywords = {
      happy: ['开心', '高兴', '快乐', '哈哈', '笑', '好棒', '太好了', '😊', '😄', '😁'],
      sad: ['难过', '伤心', '哭', '痛苦', '失望', '😢', '😭', '😞'],
      angry: ['生气', '愤怒', '讨厌', '烦', '气死了', '😠', '😡', '🤬'],
      surprised: ['惊讶', '震惊', '不敢相信', '天哪', '哇', '😮', '😲', '😱'],
      neutral: ['好的', '知道了', '明白', '嗯', '是的', '不是']
    }

    let maxScore = 0
    let detectedEmotion = 'neutral'

    for (const [emotion, keywords] of Object.entries(emotionKeywords)) {
      let score = 0
      keywords.forEach(keyword => {
        if (text.includes(keyword)) {
          score += 1
        }
      })

      if (score > maxScore) {
        maxScore = score
        detectedEmotion = emotion
      }
    }

    const confidence = Math.min(maxScore / 3, 1.0) // 最多3个关键词得满分

    return {
      emotion: detectedEmotion,
      confidence
    }
  }

  /**
   * 根据情感推荐表情
   */
  static getRecommendedExpression(emotion: string): string {
    const emotionToExpression: { [key: string]: string } = {
      happy: 'happy',
      sad: 'sad',
      angry: 'angry',
      surprised: 'surprised',
      neutral: 'default'
    }

    return emotionToExpression[emotion] || 'default'
  }
}

/**
 * 对话上下文管理器
 */
export class ConversationContext {
  private context: Map<string, any> = new Map()

  /**
   * 设置上下文
   */
  setContext(key: string, value: any) {
    this.context.set(key, value)
  }

  /**
   * 获取上下文
   */
  getContext(key: string): any {
    return this.context.get(key)
  }

  /**
   * 清除上下文
   */
  clearContext() {
    this.context.clear()
  }

  /**
   * 获取所有上下文
   */
  getAllContext(): { [key: string]: any } {
    return Object.fromEntries(this.context)
  }
}

/**
 * 智能回复生成器
 */
export class SmartReplyGenerator {
  /**
   * 生成快捷回复建议
   */
  static generateQuickReplies(lastMessage: string): string[] {
    const replies: { [key: string]: string[] } = {
      greeting: ['你好！', '很高兴见到你', '今天过得怎么样？'],
      question: ['让我想想...', '这是个好问题', '我来帮你解答'],
      compliment: ['谢谢夸奖！', '你也很棒呢', '真是太开心了'],
      goodbye: ['再见！', '下次再聊', '期待下次见面']
    }

    // 简单的意图识别
    if (lastMessage.includes('你好') || lastMessage.includes('hi')) {
      return replies.greeting
    } else if (lastMessage.includes('?') || lastMessage.includes('？')) {
      return replies.question
    } else if (lastMessage.includes('棒') || lastMessage.includes('好')) {
      return replies.compliment
    } else if (lastMessage.includes('再见') || lastMessage.includes('bye')) {
      return replies.goodbye
    }

    return ['好的', '我明白了', '还有什么想聊的吗？']
  }
}
