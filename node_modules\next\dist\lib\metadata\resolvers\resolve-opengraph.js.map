{"version": 3, "sources": ["../../../../src/lib/metadata/resolvers/resolve-opengraph.ts"], "names": ["resolveImages", "resolveOpenGraph", "resolveTwitter", "Og<PERSON><PERSON><PERSON><PERSON>s", "article", "song", "playlist", "radio", "video", "basic", "images", "metadataBase", "resolvedImages", "resolveAsArrayOrUndefined", "nonNullableImages", "item", "isItemUrl", "isStringOrURL", "inputUrl", "url", "push", "resolveUrl", "getFieldsByOgType", "ogType", "openGraph", "pathname", "titleTemplate", "resolveProps", "target", "og", "type", "undefined", "keys", "k", "key", "value", "arrayValue", "imageMetadataBase", "getSocialImageFallbackMetadataBase", "resolved", "title", "resolveTitle", "resolveAbsoluteUrlWithPathname", "TwitterBasicInfoKeys", "twitter", "card", "infoKey", "length", "players", "app"], "mappings": ";;;;;;;;;;;;;;;;IA4CgBA,aAAa;eAAbA;;IAoDHC,gBAAgB;eAAhBA;;IA8CAC,cAAc;eAAdA;;;uBAnI6B;4BAMnC;8BACsB;AAE7B,MAAMC,eAAe;IACnBC,SAAS;QAAC;QAAW;KAAO;IAC5BC,MAAM;QAAC;QAAU;KAAY;IAC7BC,UAAU;QAAC;QAAU;KAAY;IACjCC,OAAO;QAAC;KAAW;IACnBC,OAAO;QAAC;QAAU;QAAa;QAAW;KAAO;IACjDC,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;KACD;AACH;AAUO,SAAST,cACdU,MAA+C,EAC/CC,YAA8C;IAI9C,MAAMC,iBAAiBC,IAAAA,gCAAyB,EAACH;IACjD,IAAI,CAACE,gBAAgB,OAAOA;IAE5B,MAAME,oBAAoB,EAAE;IAC5B,KAAK,MAAMC,QAAQH,eAAgB;QACjC,IAAI,CAACG,MAAM;QACX,MAAMC,YAAYC,IAAAA,yBAAa,EAACF;QAChC,MAAMG,WAAWF,YAAYD,OAAOA,KAAKI,GAAG;QAC5C,IAAI,CAACD,UAAU;QAEfJ,kBAAkBM,IAAI,CACpBJ,YACI;YACEG,KAAKE,IAAAA,sBAAU,EAACN,MAAMJ;QACxB,IACA;YACE,GAAGI,IAAI;YACP,8BAA8B;YAC9BI,KAAKE,IAAAA,sBAAU,EAACN,KAAKI,GAAG,EAAER;QAC5B;IAER;IAEA,OAAOG;AACT;AAEA,SAASQ,kBAAkBC,MAAiC;IAC1D,OAAQA;QACN,KAAK;QACL,KAAK;YACH,OAAOpB,aAAaC,OAAO;QAC7B,KAAK;QACL,KAAK;YACH,OAAOD,aAAaE,IAAI;QAC1B,KAAK;YACH,OAAOF,aAAaG,QAAQ;QAC9B,KAAK;YACH,OAAOH,aAAaI,KAAK;QAC3B,KAAK;QACL,KAAK;YACH,OAAOJ,aAAaK,KAAK;QAC3B;YACE,OAAOL,aAAaM,KAAK;IAC7B;AACF;AAEO,MAAMR,mBAGT,CAACuB,WAAWb,cAAc,EAAEc,QAAQ,EAAE,EAAEC;IAC1C,IAAI,CAACF,WAAW,OAAO;IAEvB,SAASG,aAAaC,MAAyB,EAAEC,EAAa;QAC5D,MAAMN,SAASM,MAAM,UAAUA,KAAKA,GAAGC,IAAI,GAAGC;QAC9C,MAAMC,OAAOV,kBAAkBC;QAC/B,KAAK,MAAMU,KAAKD,KAAM;YACpB,MAAME,MAAMD;YACZ,IAAIC,OAAOL,MAAMK,QAAQ,OAAO;gBAC9B,MAAMC,QAAQN,EAAE,CAACK,IAAI;gBACrB,IAAIC,OAAO;oBACT,MAAMC,aAAavB,IAAAA,gCAAyB,EAACsB;oBAE3CP,MAAc,CAACM,IAAI,GAAGE;gBAC1B;YACF;QACF;QAEA,MAAMC,oBAAoBC,IAAAA,8CAAkC,EAAC3B;QAC7DiB,OAAOlB,MAAM,GAAGV,cAAc6B,GAAGnB,MAAM,EAAE2B;IAC3C;IAEA,MAAME,WAAW;QACf,GAAGf,SAAS;QACZgB,OAAOC,IAAAA,0BAAY,EAACjB,UAAUgB,KAAK,EAAEd;IACvC;IACAC,aAAaY,UAAUf;IAEvBe,SAASpB,GAAG,GAAGK,UAAUL,GAAG,GACxBuB,IAAAA,0CAA8B,EAAClB,UAAUL,GAAG,EAAER,cAAcc,YAC5D;IAEJ,OAAOc;AACT;AAEA,MAAMI,uBAAuB;IAC3B;IACA;IACA;IACA;IACA;CACD;AAEM,MAAMzC,iBAGT,CAAC0C,SAASjC,cAAce;QAaVa;IAZhB,IAAI,CAACK,SAAS,OAAO;IACrB,IAAIC,OAAO,UAAUD,UAAUA,QAAQC,IAAI,GAAGd;IAC9C,MAAMQ,WAAW;QACf,GAAGK,OAAO;QACVJ,OAAOC,IAAAA,0BAAY,EAACG,QAAQJ,KAAK,EAAEd;IACrC;IACA,KAAK,MAAMoB,WAAWH,qBAAsB;QAC1CJ,QAAQ,CAACO,QAAQ,GAAGF,OAAO,CAACE,QAAQ,IAAI;IAC1C;IACA,MAAMT,oBAAoBC,IAAAA,8CAAkC,EAAC3B;IAC7D4B,SAAS7B,MAAM,GAAGV,cAAc4C,QAAQlC,MAAM,EAAE2B;IAEhDQ,OAAOA,QAASN,CAAAA,EAAAA,mBAAAA,SAAS7B,MAAM,qBAAf6B,iBAAiBQ,MAAM,IAAG,wBAAwB,SAAQ;IAC1ER,SAASM,IAAI,GAAGA;IAEhB,IAAI,UAAUN,UAAU;QACtB,OAAQA,SAASM,IAAI;YACnB,KAAK;gBAAU;oBACbN,SAASS,OAAO,GAAGnC,IAAAA,gCAAyB,EAAC0B,SAASS,OAAO,KAAK,EAAE;oBACpE;gBACF;YACA,KAAK;gBAAO;oBACVT,SAASU,GAAG,GAAGV,SAASU,GAAG,IAAI,CAAC;oBAChC;gBACF;YACA;gBACE;QACJ;IACF;IAEA,OAAOV;AACT"}