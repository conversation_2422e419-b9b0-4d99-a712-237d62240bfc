{"version": 3, "sources": ["../../src/client/router.ts"], "names": ["Router", "with<PERSON><PERSON><PERSON>", "useRouter", "createRouter", "makePublicRouterInstance", "singletonRouter", "router", "readyCallbacks", "ready", "callback", "window", "push", "url<PERSON><PERSON><PERSON><PERSON>ields", "routerEvents", "core<PERSON><PERSON><PERSON><PERSON><PERSON>s", "Object", "defineProperty", "get", "events", "getRouter", "message", "Error", "for<PERSON>ach", "field", "args", "event", "on", "eventField", "char<PERSON>t", "toUpperCase", "substring", "_singletonRouter", "err", "console", "error", "isError", "stack", "React", "useContext", "RouterContext", "cb", "scopedRouter", "instance", "property", "assign", "Array", "isArray"], "mappings": "AAAA,iBAAiB;;;;;;;;;;;;;;;;;;;IAaRA,MAAM;eAANA,eAAM;;IAgHf,yDAAyD;IACzD,OAAiD;eAAjD;;IAGoBC,UAAU;eAAVA,mBAAU;;IAEdC,SAAS;eAATA;;IAqBAC,YAAY;eAAZA;;IAcAC,wBAAwB;eAAxBA;;;;gEArKE;iEACC;4CAEW;kEACV;qEA4HkB;AA9GtC,MAAMC,kBAAuC;IAC3CC,QAAQ;IACRC,gBAAgB,EAAE;IAClBC,OAAMC,QAAoB;QACxB,IAAI,IAAI,CAACH,MAAM,EAAE,OAAOG;QACxB,IAAI,OAAOC,WAAW,aAAa;YACjC,IAAI,CAACH,cAAc,CAACI,IAAI,CAACF;QAC3B;IACF;AACF;AAEA,4EAA4E;AAC5E,MAAMG,oBAAoB;IACxB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AACD,MAAMC,eAAe;IACnB;IACA;IACA;IACA;IACA;IACA;CACD;AAGD,MAAMC,mBAAmB;IACvB;IACA;IACA;IACA;IACA;IACA;CACD;AAED,iGAAiG;AACjGC,OAAOC,cAAc,CAACX,iBAAiB,UAAU;IAC/CY;QACE,OAAOjB,eAAM,CAACkB,MAAM;IACtB;AACF;AAEA,SAASC;IACP,IAAI,CAACd,gBAAgBC,MAAM,EAAE;QAC3B,MAAMc,UACJ,gCACA;QACF,MAAM,IAAIC,MAAMD;IAClB;IACA,OAAOf,gBAAgBC,MAAM;AAC/B;AAEAM,kBAAkBU,OAAO,CAAC,CAACC;IACzB,sEAAsE;IACtE,6CAA6C;IAC7C,kEAAkE;IAClE,0BAA0B;IAC1BR,OAAOC,cAAc,CAACX,iBAAiBkB,OAAO;QAC5CN;YACE,MAAMX,SAASa;YACf,OAAOb,MAAM,CAACiB,MAAM;QACtB;IACF;AACF;AAEAT,iBAAiBQ,OAAO,CAAC,CAACC;IAEtBlB,eAAuB,CAACkB,MAAM,GAAG;yCAAIC;YAAAA;;QACrC,MAAMlB,SAASa;QACf,OAAOb,MAAM,CAACiB,MAAM,IAAIC;IAC1B;AACF;AAEAX,aAAaS,OAAO,CAAC,CAACG;IACpBpB,gBAAgBG,KAAK,CAAC;QACpBR,eAAM,CAACkB,MAAM,CAACQ,EAAE,CAACD,OAAO;6CAAID;gBAAAA;;YAC1B,MAAMG,aAAa,AAAC,OAAIF,MAAMG,MAAM,CAAC,GAAGC,WAAW,KAAKJ,MAAMK,SAAS,CACrE;YAEF,MAAMC,mBAAmB1B;YACzB,IAAI0B,gBAAgB,CAACJ,WAAW,EAAE;gBAChC,IAAI;oBACFI,gBAAgB,CAACJ,WAAW,IAAIH;gBAClC,EAAE,OAAOQ,KAAK;oBACZC,QAAQC,KAAK,CAAC,AAAC,0CAAuCP;oBACtDM,QAAQC,KAAK,CACXC,IAAAA,gBAAO,EAACH,OAAO,AAAGA,IAAIZ,OAAO,GAAC,OAAIY,IAAII,KAAK,GAAKJ,MAAM;gBAE1D;YACF;QACF;IACF;AACF;MAGA,WAAe3B;AAKR,SAASH;IACd,MAAMI,SAAS+B,cAAK,CAACC,UAAU,CAACC,yCAAa;IAC7C,IAAI,CAACjC,QAAQ;QACX,MAAM,IAAIe,MACR;IAEJ;IAEA,OAAOf;AACT;AAYO,SAASH;IACd,IAAA,IAAA,OAAA,UAAA,QAAA,AAAGqB,OAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA,OAAA;QAAGA,KAAH,QAAA,SAAA,CAAA,KAA6C;IAAD;IAE5CnB,gBAAgBC,MAAM,GAAG,IAAIN,eAAM,IAAIwB;IACvCnB,gBAAgBE,cAAc,CAACe,OAAO,CAAC,CAACkB,KAAOA;IAC/CnC,gBAAgBE,cAAc,GAAG,EAAE;IAEnC,OAAOF,gBAAgBC,MAAM;AAC/B;AAMO,SAASF,yBAAyBE,MAAc;IACrD,MAAMmC,eAAenC;IACrB,MAAMoC,WAAW,CAAC;IAElB,KAAK,MAAMC,YAAY/B,kBAAmB;QACxC,IAAI,OAAO6B,YAAY,CAACE,SAAS,KAAK,UAAU;YAC9CD,QAAQ,CAACC,SAAS,GAAG5B,OAAO6B,MAAM,CAChCC,MAAMC,OAAO,CAACL,YAAY,CAACE,SAAS,IAAI,EAAE,GAAG,CAAC,GAC9CF,YAAY,CAACE,SAAS,EACtB,mCAAmC;;YACrC;QACF;QAEAD,QAAQ,CAACC,SAAS,GAAGF,YAAY,CAACE,SAAS;IAC7C;IAEA,iGAAiG;IACjGD,SAASxB,MAAM,GAAGlB,eAAM,CAACkB,MAAM;IAE/BJ,iBAAiBQ,OAAO,CAAC,CAACC;QACxBmB,QAAQ,CAACnB,MAAM,GAAG;6CAAIC;gBAAAA;;YACpB,OAAOiB,YAAY,CAAClB,MAAM,IAAIC;QAChC;IACF;IAEA,OAAOkB;AACT"}