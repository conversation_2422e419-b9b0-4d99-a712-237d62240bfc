'use client'

import { useState } from 'react'

interface ModelInfo {
  id: string
  name: string
  description: string
  thumbnail: string
  category: string
  size: string
  features: string[]
  isAvailable: boolean
  isPremium: boolean
}

interface ModelSelectorProps {
  isOpen: boolean
  onClose: () => void
  currentModel: string
  onModelSelect: (modelId: string) => void
}

export default function ModelSelector({ 
  isOpen, 
  onClose, 
  currentModel, 
  onModelSelect 
}: ModelSelectorProps) {
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [searchQuery, setSearchQuery] = useState('')

  if (!isOpen) return null

  // 模拟模型数据
  const models: ModelInfo[] = [
    {
      id: 'xiaoli',
      name: '小丽',
      description: '活泼可爱的女性角色，适合日常聊天和娱乐互动',
      thumbnail: '/models/xiaoli.jpg',
      category: 'female',
      size: '15MB',
      features: ['表情丰富', '语音甜美', '动作自然'],
      isAvailable: true,
      isPremium: false
    },
    {
      id: 'xiaoming',
      name: '小明',
      description: '阳光帅气的男性角色，专业且友好',
      thumbnail: '/models/xiaoming.jpg',
      category: 'male',
      size: '18MB',
      features: ['声音磁性', '专业形象', '商务风格'],
      isAvailable: true,
      isPremium: false
    },
    {
      id: 'aiko',
      name: 'Aiko',
      description: '日系风格的虚拟偶像，二次元风格',
      thumbnail: '/models/aiko.jpg',
      category: 'anime',
      size: '22MB',
      features: ['二次元风格', '多种服装', '特殊动作'],
      isAvailable: true,
      isPremium: true
    },
    {
      id: 'robot',
      name: '机器人助手',
      description: '科技感十足的AI助手，适合技术演示',
      thumbnail: '/models/robot.jpg',
      category: 'robot',
      size: '12MB',
      features: ['科技风格', 'LED效果', '机械动作'],
      isAvailable: false,
      isPremium: false
    },
    {
      id: 'celebrity1',
      name: '明星模型A',
      description: '基于真实明星打造的数字人模型',
      thumbnail: '/models/celebrity1.jpg',
      category: 'celebrity',
      size: '35MB',
      features: ['高度还原', '专业表演', '多种造型'],
      isAvailable: true,
      isPremium: true
    },
    {
      id: 'celebrity2',
      name: '明星模型B',
      description: '另一位知名明星的数字化身',
      thumbnail: '/models/celebrity2.jpg',
      category: 'celebrity',
      size: '32MB',
      features: ['逼真外观', '情感表达', '互动性强'],
      isAvailable: true,
      isPremium: true
    }
  ]

  const categories = [
    { id: 'all', name: '全部', icon: '🌟' },
    { id: 'female', name: '女性', icon: '👩' },
    { id: 'male', name: '男性', icon: '👨' },
    { id: 'anime', name: '二次元', icon: '🎭' },
    { id: 'robot', name: '机器人', icon: '🤖' },
    { id: 'celebrity', name: '明星', icon: '⭐' }
  ]

  const filteredModels = models.filter(model => {
    const matchesCategory = selectedCategory === 'all' || model.category === selectedCategory
    const matchesSearch = model.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         model.description.toLowerCase().includes(searchQuery.toLowerCase())
    return matchesCategory && matchesSearch
  })

  const handleModelSelect = (modelId: string) => {
    onModelSelect(modelId)
    onClose()
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-6xl h-[85vh] flex flex-col overflow-hidden">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-2xl font-bold text-gray-800">选择数字人模型</h2>
            <p className="text-gray-600 mt-1">选择一个适合的数字人角色开始对话</p>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 搜索和筛选 */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* 搜索框 */}
            <div className="flex-1 relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                type="text"
                placeholder="搜索模型..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* 分类筛选 */}
            <div className="flex space-x-2 overflow-x-auto">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg whitespace-nowrap transition-colors ${
                    selectedCategory === category.id
                      ? 'bg-blue-100 text-blue-700 border border-blue-200'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }`}
                >
                  <span>{category.icon}</span>
                  <span className="font-medium">{category.name}</span>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* 模型网格 */}
        <div className="flex-1 p-6 overflow-y-auto">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredModels.map((model) => (
              <div
                key={model.id}
                className={`relative bg-white border-2 rounded-xl overflow-hidden transition-all duration-200 hover:shadow-lg ${
                  currentModel === model.id
                    ? 'border-blue-500 shadow-lg'
                    : 'border-gray-200 hover:border-gray-300'
                } ${!model.isAvailable ? 'opacity-60' : ''}`}
              >
                {/* 缩略图 */}
                <div className="aspect-[4/3] bg-gradient-to-br from-blue-100 to-purple-100 relative overflow-hidden">
                  {/* 模拟缩略图 */}
                  <div className="absolute inset-0 flex items-center justify-center text-6xl">
                    {model.category === 'female' && '👩'}
                    {model.category === 'male' && '👨'}
                    {model.category === 'anime' && '🎭'}
                    {model.category === 'robot' && '🤖'}
                    {model.category === 'celebrity' && '⭐'}
                  </div>

                  {/* 状态标签 */}
                  <div className="absolute top-2 left-2 flex space-x-2">
                    {model.isPremium && (
                      <span className="px-2 py-1 bg-yellow-500 text-white text-xs font-medium rounded-full">
                        Premium
                      </span>
                    )}
                    {currentModel === model.id && (
                      <span className="px-2 py-1 bg-blue-500 text-white text-xs font-medium rounded-full">
                        当前使用
                      </span>
                    )}
                    {!model.isAvailable && (
                      <span className="px-2 py-1 bg-gray-500 text-white text-xs font-medium rounded-full">
                        即将推出
                      </span>
                    )}
                  </div>
                </div>

                {/* 模型信息 */}
                <div className="p-4">
                  <div className="flex items-start justify-between mb-2">
                    <h3 className="text-lg font-semibold text-gray-800">{model.name}</h3>
                    <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                      {model.size}
                    </span>
                  </div>

                  <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                    {model.description}
                  </p>

                  {/* 特性标签 */}
                  <div className="flex flex-wrap gap-1 mb-4">
                    {model.features.slice(0, 3).map((feature, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-blue-50 text-blue-600 text-xs rounded-full"
                      >
                        {feature}
                      </span>
                    ))}
                  </div>

                  {/* 选择按钮 */}
                  <button
                    onClick={() => handleModelSelect(model.id)}
                    disabled={!model.isAvailable}
                    className={`w-full py-2 px-4 rounded-lg font-medium transition-colors ${
                      model.isAvailable
                        ? currentModel === model.id
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                        : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    }`}
                  >
                    {!model.isAvailable
                      ? '即将推出'
                      : currentModel === model.id
                      ? '当前使用'
                      : '选择此模型'
                    }
                  </button>
                </div>
              </div>
            ))}
          </div>

          {/* 空状态 */}
          {filteredModels.length === 0 && (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">🔍</div>
              <h3 className="text-lg font-medium text-gray-800 mb-2">未找到匹配的模型</h3>
              <p className="text-gray-600">尝试调整搜索条件或选择其他分类</p>
            </div>
          )}
        </div>

        {/* 底部信息 */}
        <div className="p-6 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <div className="flex items-center space-x-4">
              <span>共 {models.length} 个模型</span>
              <span>•</span>
              <span>{models.filter(m => m.isAvailable).length} 个可用</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="w-3 h-3 bg-yellow-500 rounded-full"></span>
              <span>Premium 需要订阅</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
