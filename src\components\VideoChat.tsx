'use client'

import { useEffect, useRef, useState } from 'react'
import { FaceData } from '@/types'
import { createMediaPipeFaceDetector } from '@/lib/mediapipe-face'

interface VideoChatProps {
  onFaceData?: (faceData: FaceData) => void
  isEnabled?: boolean
}

export default function VideoChat({ onFaceData, isEnabled = true }: VideoChatProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const faceDetectorRef = useRef<any>(null)
  const [isVideoActive, setIsVideoActive] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isFaceDetected, setIsFaceDetected] = useState(false)

  useEffect(() => {
    if (isEnabled) {
      startVideo()
    }
    return () => {
      stopVideo()
      if (faceDetectorRef.current) {
        faceDetectorRef.current.dispose()
      }
    }
  }, [isEnabled])

  // 初始化面部检测器
  useEffect(() => {
    const initializeFaceDetector = async () => {
      try {
        faceDetectorRef.current = createMediaPipeFaceDetector()

        // 设置面部数据回调
        faceDetectorRef.current.setOnResults((faceData: FaceData) => {
          setIsFaceDetected(true)
          onFaceData?.(faceData)
        })

        console.log('Face detector initialized')
      } catch (error) {
        console.error('Failed to initialize face detector:', error)
      }
    }

    initializeFaceDetector()
  }, [])

  const startVideo = async () => {
    setIsLoading(true)
    setError(null)

    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 640 },
          height: { ideal: 480 },
          facingMode: 'user'
        },
        audio: false
      })

      if (videoRef.current) {
        videoRef.current.srcObject = stream
        setIsVideoActive(true)

        // 启动面部检测
        if (faceDetectorRef.current) {
          try {
            await faceDetectorRef.current.startDetection(videoRef.current)
            console.log('Face detection started')
          } catch (error) {
            console.error('Failed to start face detection:', error)
          }
        }
      }
    } catch (err) {
      console.error('Error accessing camera:', err)
      setError('无法访问摄像头，请检查权限设置')
    } finally {
      setIsLoading(false)
    }
  }

  const stopVideo = () => {
    // 停止面部检测
    if (faceDetectorRef.current) {
      faceDetectorRef.current.stopDetection()
    }

    if (videoRef.current && videoRef.current.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream
      stream.getTracks().forEach(track => track.stop())
      videoRef.current.srcObject = null
      setIsVideoActive(false)
      setIsFaceDetected(false)
    }
  }

  const toggleVideo = () => {
    if (isVideoActive) {
      stopVideo()
    } else {
      startVideo()
    }
  }

  return (
    <div className="relative w-full h-full bg-gray-900 rounded-lg overflow-hidden shadow-lg">
      {/* 视频流 */}
      {isVideoActive && (
        <video
          ref={videoRef}
          autoPlay
          playsInline
          muted
          className="w-full h-full object-cover"
        />
      )}

      {/* 加载状态 */}
      {isLoading && (
        <div className="absolute inset-0 bg-gray-800 flex items-center justify-center">
          <div className="text-center text-white">
            <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
            <p className="text-sm">启动摄像头...</p>
          </div>
        </div>
      )}

      {/* 错误状态 */}
      {error && (
        <div className="absolute inset-0 bg-gray-800 flex items-center justify-center p-4">
          <div className="text-center text-white">
            <svg className="w-12 h-12 mx-auto mb-2 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p className="text-sm">{error}</p>
            <button
              onClick={startVideo}
              className="mt-2 px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700 transition-colors"
            >
              重试
            </button>
          </div>
        </div>
      )}

      {/* 无视频状态 */}
      {!isVideoActive && !isLoading && !error && (
        <div className="absolute inset-0 bg-gray-800 flex items-center justify-center">
          <div className="text-center text-white">
            <svg className="w-12 h-12 mx-auto mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
            </svg>
            <p className="text-sm">摄像头已关闭</p>
          </div>
        </div>
      )}

      {/* 控制按钮 */}
      <div className="absolute bottom-2 left-2 right-2 flex justify-center space-x-2">
        <button
          onClick={toggleVideo}
          className={`p-2 rounded-full transition-colors ${
            isVideoActive
              ? 'bg-red-600 hover:bg-red-700 text-white'
              : 'bg-green-600 hover:bg-green-700 text-white'
          }`}
          title={isVideoActive ? '关闭摄像头' : '开启摄像头'}
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            {isVideoActive ? (
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728" />
            ) : (
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
            )}
          </svg>
        </button>
      </div>

      {/* 面部识别指示器 */}
      {isVideoActive && (
        <div className="absolute top-2 left-2">
          <div className={`flex items-center space-x-1 text-white px-2 py-1 rounded text-xs ${
            isFaceDetected ? 'bg-green-600/80' : 'bg-yellow-600/80'
          }`}>
            <div className={`w-2 h-2 rounded-full ${
              isFaceDetected ? 'bg-green-300 animate-pulse' : 'bg-yellow-300'
            }`}></div>
            <span>{isFaceDetected ? '面部已检测' : '搜索面部中'}</span>
          </div>
        </div>
      )}

      {/* 性能指示器 */}
      {isVideoActive && process.env.NODE_ENV === 'development' && (
        <div className="absolute top-2 right-2 bg-black/50 text-white px-2 py-1 rounded text-xs font-mono">
          <div>30 FPS</div>
        </div>
      )}
    </div>
  )
}
