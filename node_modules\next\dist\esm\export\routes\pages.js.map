{"version": 3, "sources": ["../../../src/export/routes/pages.ts"], "names": ["RenderResult", "join", "isInAmpMode", "NEXT_DATA_SUFFIX", "SERVER_PROPS_EXPORT_ERROR", "NEXT_DYNAMIC_NO_SSR_CODE", "AmpHtmlValidator", "FileType", "fileExists", "lazyRenderPagesPage", "ExportedPagesFiles", "HTML", "DATA", "AMP_HTML", "AMP_DATA", "exportPages", "req", "res", "path", "page", "query", "htmlFilepath", "htmlFilename", "ampPath", "subFolders", "outDir", "ampValidator<PERSON>ath", "pagesDataDir", "buildExport", "isDynamic", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "renderOpts", "components", "fileWriter", "ampState", "ampFirs<PERSON>", "pageConfig", "amp", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "hybrid", "inAmpMode", "hybridAmp", "getServerSideProps", "Error", "getStaticProps", "endsWith", "renderResult", "Component", "fromStatic", "optimizeFonts", "process", "env", "__NEXT_OPTIMIZE_FONTS", "JSON", "stringify", "optimizeCss", "__NEXT_OPTIMIZE_CSS", "err", "digest", "ssgNotFound", "metadata", "isNotFound", "ampValidations", "validateAmp", "rawAmpHtml", "ampPageName", "validatorPath", "validator", "getInstance", "result", "validateString", "errors", "filter", "e", "severity", "warnings", "length", "push", "html", "isNull", "toUnchunkedString", "ampRenderResult", "ampSkipValidation", "ampHtmlFilename", "ampHtmlFilepath", "exists", "File", "ampHtml", "pageData", "dataFile", "replace", "revalidate"], "mappings": "AAMA,OAAOA,kBAAkB,6BAA4B;AACrD,SAASC,IAAI,QAAQ,OAAM;AAK3B,SAASC,WAAW,QAAQ,4BAA2B;AACvD,SACEC,gBAAgB,EAChBC,yBAAyB,QACpB,sBAAqB;AAC5B,SAASC,wBAAwB,QAAQ,6CAA4C;AACrF,OAAOC,sBAAsB,uCAAsC;AACnE,SAASC,QAAQ,EAAEC,UAAU,QAAQ,wBAAuB;AAC5D,SAASC,mBAAmB,QAAQ,wDAAuD;WAEpF;UAAWC,kBAAkB;IAAlBA,mBAChBC,UAAAA;IADgBD,mBAEhBE,UAAAA;IAFgBF,mBAGhBG,cAAAA;IAHgBH,mBAIhBI,cAAW;GAJKJ,uBAAAA;AAOlB,OAAO,eAAeK,YACpBC,GAAkB,EAClBC,GAAmB,EACnBC,IAAY,EACZC,IAAY,EACZC,KAAyB,EACzBC,YAAoB,EACpBC,YAAoB,EACpBC,OAAe,EACfC,UAAmB,EACnBC,MAAc,EACdC,gBAAoC,EACpCC,YAAoB,EACpBC,WAAoB,EACpBC,SAAkB,EAClBC,kBAA2B,EAC3BC,UAAsB,EACtBC,UAAoC,EACpCC,UAAsB;QAGVD,wBAEFA;IAHV,MAAME,WAAW;QACfC,UAAUH,EAAAA,yBAAAA,WAAWI,UAAU,qBAArBJ,uBAAuBK,GAAG,MAAK;QACzCC,UAAUC,QAAQnB,MAAMiB,GAAG;QAC3BG,QAAQR,EAAAA,0BAAAA,WAAWI,UAAU,qBAArBJ,wBAAuBK,GAAG,MAAK;IACzC;IAEA,MAAMI,YAAYvC,YAAYgC;IAC9B,MAAMQ,YAAYR,SAASM,MAAM;IAEjC,IAAIR,WAAWW,kBAAkB,EAAE;QACjC,MAAM,IAAIC,MAAM,CAAC,eAAe,EAAEzB,KAAK,EAAE,EAAEf,0BAA0B,CAAC;IACxE;IAEA,mDAAmD;IACnD,uBAAuB;IACvB,IAAI,CAACwB,eAAeI,WAAWa,cAAc,IAAI,CAAChB,WAAW;QAC3D;IACF;IAEA,IAAIG,WAAWa,cAAc,IAAI,CAACxB,aAAayB,QAAQ,CAAC,UAAU;QAChE,0DAA0D;QAC1DzB,gBAAgB;QAChBC,gBAAgB;IAClB;IAEA,IAAIyB;IAEJ,IAAI,OAAOf,WAAWgB,SAAS,KAAK,UAAU;QAC5CD,eAAe/C,aAAaiD,UAAU,CAACjB,WAAWgB,SAAS;QAE3D,IAAIlB,oBAAoB;YACtB,MAAM,IAAIc,MACR,CAAC,uCAAuC,EAAE1B,KAAK,mLAAmL,CAAC;QAEvO;IACF,OAAO;QACL;;;;;KAKC,GACD,IAAIa,WAAWmB,aAAa,EAAE;YAC5BC,QAAQC,GAAG,CAACC,qBAAqB,GAAGC,KAAKC,SAAS,CAChDxB,WAAWmB,aAAa;QAE5B;QACA,IAAInB,WAAWyB,WAAW,EAAE;YAC1BL,QAAQC,GAAG,CAACK,mBAAmB,GAAGH,KAAKC,SAAS,CAAC;QACnD;QACA,IAAI;YACFR,eAAe,MAAMtC,oBACnBO,KACAC,KACAE,MACAC,OACAW;QAEJ,EAAE,OAAO2B,KAAU;YACjB,IAAIA,IAAIC,MAAM,KAAKtD,0BAA0B;gBAC3C,MAAMqD;YACR;QACF;IACF;IAEA,MAAME,cAAcb,gCAAAA,aAAcc,QAAQ,CAACC,UAAU;IAErD,MAAMC,iBAAkC,EAAE;IAE1C,MAAMC,cAAc,OAClBC,YACAC,aACAC;QAEA,MAAMC,YAAY,MAAM9D,iBAAiB+D,WAAW,CAACF;QACrD,MAAMG,SAASF,UAAUG,cAAc,CAACN;QACxC,MAAMO,SAASF,OAAOE,MAAM,CAACC,MAAM,CAAC,CAACC,IAAMA,EAAEC,QAAQ,KAAK;QAC1D,MAAMC,WAAWN,OAAOE,MAAM,CAACC,MAAM,CAAC,CAACC,IAAMA,EAAEC,QAAQ,KAAK;QAE5D,IAAIC,SAASC,MAAM,IAAIL,OAAOK,MAAM,EAAE;YACpCd,eAAee,IAAI,CAAC;gBAClB3D,MAAM+C;gBACNI,QAAQ;oBACNE;oBACAI;gBACF;YACF;QACF;IACF;IAEA,MAAMG,OACJhC,gBAAgB,CAACA,aAAaiC,MAAM,GAAGjC,aAAakC,iBAAiB,KAAK;IAE5E,IAAIC;IAEJ,IAAIzC,aAAa,CAACV,WAAWoD,iBAAiB,EAAE;QAC9C,IAAI,CAACvB,aAAa;YAChB,MAAMI,YAAYe,MAAM7D,MAAMQ;QAChC;IACF,OAAO,IAAIgB,WAAW;QACpB,MAAM0C,kBAAkB5D,aACpBvB,KAAKsB,SAAS,gBACd,CAAC,EAAEA,QAAQ,KAAK,CAAC;QAErB,MAAM8D,kBAAkBpF,KAAKwB,QAAQ2D;QAErC,MAAME,SAAS,MAAM9E,WAAW6E,iBAAiB9E,SAASgF,IAAI;QAC9D,IAAI,CAACD,QAAQ;YACX,IAAI;gBACFJ,kBAAkB,MAAMzE,oBACtBO,KACAC,KACAE,MACA;oBAAE,GAAGC,KAAK;oBAAEiB,KAAK;gBAAI,GACrBN;YAEJ,EAAE,OAAO2B,KAAU;gBACjB,IAAIA,IAAIC,MAAM,KAAKtD,0BAA0B;oBAC3C,MAAMqD;gBACR;YACF;YAEA,MAAM8B,UACJN,mBAAmB,CAACA,gBAAgBF,MAAM,GACtCE,gBAAgBD,iBAAiB,KACjC;YACN,IAAI,CAAClD,WAAWoD,iBAAiB,EAAE;gBACjC,MAAMnB,YAAYwB,SAASrE,OAAO;YACpC;YAEA,MAAMc,WA1JC,YA4JLoD,iBACAG,SACA;QAEJ;IACF;IAEA,MAAM3B,WAAWd,CAAAA,gCAAAA,aAAcc,QAAQ,MAAIqB,mCAAAA,gBAAiBrB,QAAQ,KAAI,CAAC;IACzE,IAAIA,SAAS4B,QAAQ,EAAE;QACrB,MAAMC,WAAWzF,KACf0B,cACAL,aAAaqE,OAAO,CAAC,WAAWxF;QAGlC,MAAM8B,WA3KD,QA6KHyD,UACApC,KAAKC,SAAS,CAACM,SAAS4B,QAAQ,GAChC;QAGF,IAAI/C,WAAW;YACb,MAAMT,WAjLC,iBAmLLyD,SAASC,OAAO,CAAC,WAAW,cAC5BrC,KAAKC,SAAS,CAACM,SAAS4B,QAAQ,GAChC;QAEJ;IACF;IAEA,IAAI,CAAC7B,aAAa;QAChB,qEAAqE;QACrE,MAAM3B,WA/LD,QA+LqCZ,cAAc0D,MAAM;IAChE;IAEA,OAAO;QACLhB;QACA6B,YAAY/B,SAAS+B,UAAU,IAAI;QACnChC;IACF;AACF"}