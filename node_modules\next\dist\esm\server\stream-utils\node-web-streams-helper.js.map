{"version": 3, "sources": ["../../../src/server/stream-utils/node-web-streams-helper.ts"], "names": ["getTracer", "AppRenderSpan", "createDecodeTransformStream", "Detached<PERSON>romise", "scheduleImmediate", "cloneTransformStream", "source", "sourceReader", "readable", "<PERSON><PERSON><PERSON><PERSON>", "clone", "TransformStream", "start", "controller", "done", "value", "read", "enqueue", "transform", "chainStreams", "streams", "writable", "promise", "Promise", "resolve", "i", "length", "then", "pipeTo", "preventClose", "catch", "streamFromString", "str", "encoder", "TextEncoder", "ReadableStream", "encode", "close", "streamToString", "stream", "buffer", "pipeThrough", "WritableStream", "write", "chunk", "createBufferedTransformStream", "Uint8Array", "pending", "flush", "detached", "undefined", "combined", "byteLength", "set", "createInsertedHTMLStream", "getServerInsertedHTML", "html", "renderToInitialFizzStream", "ReactDOMServer", "element", "streamOptions", "trace", "renderToReadableStream", "createHeadInsertionTransformStream", "insert", "inserted", "freezing", "decoder", "TextDecoder", "insertion", "content", "decode", "index", "indexOf", "insertedHeadContent", "slice", "createDeferredSuffixStream", "suffix", "flushed", "createMergedTransformStream", "started", "reader", "err", "error", "createMoveSuffixStream", "foundSuffix", "buf", "before", "after", "createRootLayoutValidatorStream", "assetPrefix", "getTree", "foundHtml", "foundBody", "includes", "missingTags", "push", "JSON", "stringify", "tree", "chainTransformers", "transformers", "transformer", "continueFizzStream", "renderStream", "inlinedDataStream", "isStaticGeneration", "serverInsertedHTMLToHead", "validateRootLayout", "closeTag", "suffixUnclosed", "split", "allReady", "continuePostponedFizzStream"], "mappings": "AAEA,SAASA,SAAS,QAAQ,sBAAqB;AAC/C,SAASC,aAAa,QAAQ,yBAAwB;AACtD,SAASC,2BAA2B,QAAQ,kBAAiB;AAC7D,SAASC,eAAe,QAAQ,6BAA4B;AAC5D,SAASC,iBAAiB,QAAQ,sBAAqB;AAMvD,OAAO,SAASC,qBAAqBC,MAAuB;IAC1D,MAAMC,eAAeD,OAAOE,QAAQ,CAACC,SAAS;IAC9C,MAAMC,QAAQ,IAAIC,gBAAgB;QAChC,MAAMC,OAAMC,UAAU;YACpB,MAAO,KAAM;gBACX,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMR,aAAaS,IAAI;gBAC/C,IAAIF,MAAM;oBACR;gBACF;gBACAD,WAAWI,OAAO,CAACF;YACrB;QACF;QACA,wBAAwB;QACxBG,cAAa;IACf;IAEA,OAAOR;AACT;AAEA,OAAO,SAASS,aACd,GAAGC,OAA4B;IAE/B,MAAM,EAAEZ,QAAQ,EAAEa,QAAQ,EAAE,GAAG,IAAIV;IAEnC,IAAIW,UAAUC,QAAQC,OAAO;IAC7B,IAAK,IAAIC,IAAI,GAAGA,IAAIL,QAAQM,MAAM,EAAE,EAAED,EAAG;QACvCH,UAAUA,QAAQK,IAAI,CAAC,IACrBP,OAAO,CAACK,EAAE,CAACG,MAAM,CAACP,UAAU;gBAAEQ,cAAcJ,IAAI,IAAIL,QAAQM,MAAM;YAAC;IAEvE;IAEA,0EAA0E;IAC1E,gDAAgD;IAChDJ,QAAQQ,KAAK,CAAC,KAAO;IAErB,OAAOtB;AACT;AAEA,OAAO,SAASuB,iBAAiBC,GAAW;IAC1C,MAAMC,UAAU,IAAIC;IACpB,OAAO,IAAIC,eAAe;QACxBvB,OAAMC,UAAU;YACdA,WAAWI,OAAO,CAACgB,QAAQG,MAAM,CAACJ;YAClCnB,WAAWwB,KAAK;QAClB;IACF;AACF;AAEA,OAAO,eAAeC,eACpBC,MAAkC;IAElC,IAAIC,SAAS;IAEb,MAAMD,MACJ,wDAAwD;KACvDE,WAAW,CAACvC,+BACZ0B,MAAM,CACL,IAAIc,eAAuB;QACzBC,OAAMC,KAAK;YACTJ,UAAUI;QACZ;IACF;IAGJ,OAAOJ;AACT;AAEA,OAAO,SAASK;IAId,IAAIL,SAAqB,IAAIM;IAC7B,IAAIC;IAEJ,MAAMC,QAAQ,CAACnC;QACb,yDAAyD;QACzD,IAAIkC,SAAS;QAEb,MAAME,WAAW,IAAI9C;QACrB4C,UAAUE;QAEV7C,kBAAkB;YAChB,IAAI;gBACFS,WAAWI,OAAO,CAACuB;gBACnBA,SAAS,IAAIM;YACf,EAAE,OAAM;YACN,6DAA6D;YAC7D,8DAA8D;YAC9D,6CAA6C;YAC/C,SAAU;gBACRC,UAAUG;gBACVD,SAASzB,OAAO;YAClB;QACF;IACF;IAEA,OAAO,IAAIb,gBAAgB;QACzBO,WAAU0B,KAAK,EAAE/B,UAAU;YACzB,kDAAkD;YAClD,MAAMsC,WAAW,IAAIL,WAAWN,OAAOd,MAAM,GAAGkB,MAAMQ,UAAU;YAChED,SAASE,GAAG,CAACb;YACbW,SAASE,GAAG,CAACT,OAAOJ,OAAOd,MAAM;YACjCc,SAASW;YAET,sCAAsC;YACtCH,MAAMnC;QACR;QACAmC;YACE,IAAI,CAACD,SAAS;YAEd,OAAOA,QAAQzB,OAAO;QACxB;IACF;AACF;AAEA,SAASgC,yBACPC,qBAA4C;IAE5C,MAAMtB,UAAU,IAAIC;IACpB,OAAO,IAAIvB,gBAAgB;QACzBO,WAAW,OAAO0B,OAAO/B;YACvB,MAAM2C,OAAO,MAAMD;YACnB,IAAIC,MAAM;gBACR3C,WAAWI,OAAO,CAACgB,QAAQG,MAAM,CAACoB;YACpC;YAEA3C,WAAWI,OAAO,CAAC2B;QACrB;IACF;AACF;AAEA,OAAO,SAASa,0BAA0B,EACxCC,cAAc,EACdC,OAAO,EACPC,aAAa,EAKd;IACC,OAAO5D,YAAY6D,KAAK,CAAC5D,cAAc6D,sBAAsB,EAAE,UAC7DJ,eAAeI,sBAAsB,CAACH,SAASC;AAEnD;AAEA,SAASG,mCACPC,MAA6B;IAE7B,IAAIC,WAAW;IACf,IAAIC,WAAW;IAEf,MAAMjC,UAAU,IAAIC;IACpB,MAAMiC,UAAU,IAAIC;IAEpB,OAAO,IAAIzD,gBAAgB;QACzB,MAAMO,WAAU0B,KAAK,EAAE/B,UAAU;YAC/B,4DAA4D;YAC5D,IAAIqD,UAAU;gBACZrD,WAAWI,OAAO,CAAC2B;gBACnB;YACF;YAEA,MAAMyB,YAAY,MAAML;YACxB,IAAIC,UAAU;gBACZpD,WAAWI,OAAO,CAACgB,QAAQG,MAAM,CAACiC;gBAClCxD,WAAWI,OAAO,CAAC2B;gBACnBsB,WAAW;YACb,OAAO;gBACL,MAAMI,UAAUH,QAAQI,MAAM,CAAC3B;gBAC/B,MAAM4B,QAAQF,QAAQG,OAAO,CAAC;gBAC9B,IAAID,UAAU,CAAC,GAAG;oBAChB,MAAME,sBACJJ,QAAQK,KAAK,CAAC,GAAGH,SAASH,YAAYC,QAAQK,KAAK,CAACH;oBACtD3D,WAAWI,OAAO,CAACgB,QAAQG,MAAM,CAACsC;oBAClCR,WAAW;oBACXD,WAAW;gBACb;YACF;YAEA,IAAI,CAACA,UAAU;gBACbpD,WAAWI,OAAO,CAAC2B;YACrB,OAAO;gBACLxC,kBAAkB;oBAChB8D,WAAW;gBACb;YACF;QACF;QACA,MAAMlB,OAAMnC,UAAU;YACpB,gEAAgE;YAChE,MAAMwD,YAAY,MAAML;YACxB,IAAIK,WAAW;gBACbxD,WAAWI,OAAO,CAACgB,QAAQG,MAAM,CAACiC;YACpC;QACF;IACF;AACF;AAEA,2DAA2D;AAC3D,gDAAgD;AAChD,SAASO,2BACPC,MAAc;IAEd,IAAIC,UAAU;IACd,IAAI/B;IAEJ,MAAMd,UAAU,IAAIC;IAEpB,MAAMc,QAAQ,CAACnC;QACb,MAAMoC,WAAW,IAAI9C;QACrB4C,UAAUE;QAEV7C,kBAAkB;YAChB,IAAI;gBACFS,WAAWI,OAAO,CAACgB,QAAQG,MAAM,CAACyC;YACpC,EAAE,OAAM;YACN,6DAA6D;YAC7D,8DAA8D;YAC9D,6CAA6C;YAC/C,SAAU;gBACR9B,UAAUG;gBACVD,SAASzB,OAAO;YAClB;QACF;IACF;IAEA,OAAO,IAAIb,gBAAgB;QACzBO,WAAU0B,KAAK,EAAE/B,UAAU;YACzBA,WAAWI,OAAO,CAAC2B;YAEnB,wCAAwC;YACxC,IAAIkC,SAAS;YAEb,gCAAgC;YAChCA,UAAU;YACV9B,MAAMnC;QACR;QACAmC,OAAMnC,UAAU;YACd,IAAIkC,SAAS,OAAOA,QAAQzB,OAAO;YACnC,IAAIwD,SAAS;YAEb,aAAa;YACbjE,WAAWI,OAAO,CAACgB,QAAQG,MAAM,CAACyC;QACpC;IACF;AACF;AAEA,0EAA0E;AAC1E,0BAA0B;AAC1B,SAASE,4BACPxC,MAAkC;IAElC,IAAIyC,UAAU;IACd,IAAIjC,UAAwC;IAE5C,MAAMnC,QAAQ,CAACC;QACb,MAAMoE,SAAS1C,OAAO9B,SAAS;QAE/B,wBAAwB;QACxB,gEAAgE;QAChE,qEAAqE;QACrE,uEAAuE;QACvE,8DAA8D;QAC9D,aAAa;QACb,MAAMwC,WAAW,IAAI9C;QACrB4C,UAAUE;QAEV,2EAA2E;QAC3E,4EAA4E;QAC5E,wCAAwC;QACxC7C,kBAAkB;YAChB,IAAI;gBACF,MAAO,KAAM;oBACX,MAAM,EAAEU,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMkE,OAAOjE,IAAI;oBACzC,IAAIF,MAAM;oBAEVD,WAAWI,OAAO,CAACF;gBACrB;YACF,EAAE,OAAOmE,KAAK;gBACZrE,WAAWsE,KAAK,CAACD;YACnB,SAAU;gBACRjC,SAASzB,OAAO;YAClB;QACF;IACF;IAEA,OAAO,IAAIb,gBAAgB;QACzBO,WAAU0B,KAAK,EAAE/B,UAAU;YACzBA,WAAWI,OAAO,CAAC2B;YAEnB,6DAA6D;YAC7D,IAAIoC,SAAS;YACbA,UAAU;YAEVpE,MAAMC;QACR;QACAmC;YACE,0EAA0E;YAC1E,wCAAwC;YACxC,IAAI,CAACD,SAAS;YACd,IAAI,CAACiC,SAAS;YAEd,OAAOjC,QAAQzB,OAAO;QACxB;IACF;AACF;AAEA;;;;CAIC,GACD,SAAS8D,uBACPP,MAAc;IAEd,IAAIQ,cAAc;IAElB,MAAMpD,UAAU,IAAIC;IACpB,MAAMiC,UAAU,IAAIC;IAEpB,OAAO,IAAIzD,gBAAgB;QACzBO,WAAU0B,KAAK,EAAE/B,UAAU;YACzB,IAAIwE,aAAa;gBACf,OAAOxE,WAAWI,OAAO,CAAC2B;YAC5B;YAEA,MAAM0C,MAAMnB,QAAQI,MAAM,CAAC3B;YAC3B,MAAM4B,QAAQc,IAAIb,OAAO,CAACI;YAC1B,IAAIL,QAAQ,CAAC,GAAG;gBACda,cAAc;gBAEd,uEAAuE;gBACvE,2BAA2B;gBAC3B,IAAIC,IAAI5D,MAAM,KAAKmD,OAAOnD,MAAM,EAAE;oBAChC;gBACF;gBAEA,wCAAwC;gBACxC,MAAM6D,SAASD,IAAIX,KAAK,CAAC,GAAGH;gBAC5B5B,QAAQX,QAAQG,MAAM,CAACmD;gBACvB1E,WAAWI,OAAO,CAAC2B;gBAEnB,sEAAsE;gBACtE,qCAAqC;gBACrC,IAAI0C,IAAI5D,MAAM,GAAGmD,OAAOnD,MAAM,GAAG8C,OAAO;oBACtC,uCAAuC;oBACvC,MAAMgB,QAAQF,IAAIX,KAAK,CAACH,QAAQK,OAAOnD,MAAM;oBAC7CkB,QAAQX,QAAQG,MAAM,CAACoD;oBACvB3E,WAAWI,OAAO,CAAC2B;gBACrB;YACF,OAAO;gBACL/B,WAAWI,OAAO,CAAC2B;YACrB;QACF;QACAI,OAAMnC,UAAU;YACd,uEAAuE;YACvE,mCAAmC;YACnCA,WAAWI,OAAO,CAACgB,QAAQG,MAAM,CAACyC;QACpC;IACF;AACF;AAEA,OAAO,SAASY,gCACdC,cAAc,EAAE,EAChBC,OAAgC;IAEhC,IAAIC,YAAY;IAChB,IAAIC,YAAY;IAEhB,MAAM5D,UAAU,IAAIC;IACpB,MAAMiC,UAAU,IAAIC;IAEpB,IAAIE,UAAU;IACd,OAAO,IAAI3D,gBAAgB;QACzB,MAAMO,WAAU0B,KAAK,EAAE/B,UAAU;YAC/B,+DAA+D;YAC/D,IAAI,CAAC+E,aAAa,CAACC,WAAW;gBAC5BvB,WAAWH,QAAQI,MAAM,CAAC3B,OAAO;oBAAEL,QAAQ;gBAAK;gBAChD,IAAI,CAACqD,aAAatB,QAAQwB,QAAQ,CAAC,UAAU;oBAC3CF,YAAY;gBACd;gBACA,IAAI,CAACC,aAAavB,QAAQwB,QAAQ,CAAC,UAAU;oBAC3CD,YAAY;gBACd;YACF;YACAhF,WAAWI,OAAO,CAAC2B;QACrB;QACAI,OAAMnC,UAAU;YACd,qBAAqB;YACrB,IAAI,CAAC+E,aAAa,CAACC,WAAW;gBAC5BvB,WAAWH,QAAQI,MAAM;gBACzB,IAAI,CAACqB,aAAatB,QAAQwB,QAAQ,CAAC,UAAU;oBAC3CF,YAAY;gBACd;gBACA,IAAI,CAACC,aAAavB,QAAQwB,QAAQ,CAAC,UAAU;oBAC3CD,YAAY;gBACd;YACF;YAEA,uEAAuE;YACvE,cAAc;YACd,MAAME,cAAwB,EAAE;YAChC,IAAI,CAACH,WAAWG,YAAYC,IAAI,CAAC;YACjC,IAAI,CAACH,WAAWE,YAAYC,IAAI,CAAC;YAEjC,IAAID,YAAYrE,MAAM,GAAG,GAAG;gBAC1Bb,WAAWI,OAAO,CAChBgB,QAAQG,MAAM,CACZ,CAAC,mDAAmD,EAAE6D,KAAKC,SAAS,CAClE;oBAAEH;oBAAaL,aAAaA,eAAe;oBAAIS,MAAMR;gBAAU,GAC/D,SAAS,CAAC;YAGlB;QACF;IACF;AACF;AAEA,SAASS,kBACP5F,QAA2B,EAC3B6F,YAAyD;IAEzD,IAAI9D,SAAS/B;IACb,KAAK,MAAM8F,eAAeD,aAAc;QACtC,IAAI,CAACC,aAAa;QAElB/D,SAASA,OAAOE,WAAW,CAAC6D;IAC9B;IACA,OAAO/D;AACT;AAmBA,OAAO,eAAegE,mBACpBC,YAAiC,EACjC,EACE3B,MAAM,EACN4B,iBAAiB,EACjBC,kBAAkB,EAClBnD,qBAAqB,EACrBoD,wBAAwB,EACxBC,kBAAkB,EACI;IAExB,MAAMC,WAAW;IAEjB,6EAA6E;IAC7E,MAAMC,iBAAiBjC,SAASA,OAAOkC,KAAK,CAACF,UAAU,EAAE,CAAC,EAAE,GAAG;IAE/D,2EAA2E;IAC3E,+DAA+D;IAC/D,IAAIH,sBAAsB,cAAcF,cAAc;QACpD,MAAMA,aAAaQ,QAAQ;IAC7B;IAEA,OAAOZ,kBAAkBI,cAAc;QACrC,qDAAqD;QACrD3D;QAEA,gCAAgC;QAChCU,yBAAyB,CAACoD,2BACtBrD,yBAAyBC,yBACzB;QAEJ,wBAAwB;QACxBuD,kBAAkB,QAAQA,eAAepF,MAAM,GAAG,IAC9CkD,2BAA2BkC,kBAC3B;QAEJ,+EAA+E;QAC/EL,oBAAoB1B,4BAA4B0B,qBAAqB;QAErE,kDAAkD;QAClDrB,uBAAuByB;QAEvB,0BAA0B;QAC1B,qFAAqF;QACrF,+EAA+E;QAC/EtD,yBAAyBoD,2BACrB5C,mCAAmCR,yBACnC;QAEJqD,qBACInB,gCACEmB,mBAAmBlB,WAAW,EAC9BkB,mBAAmBjB,OAAO,IAE5B;KACL;AACH;AAUA,OAAO,eAAesB,4BACpBT,YAAiC,EACjC,EACEC,iBAAiB,EACjBC,kBAAkB,EAClBnD,qBAAqB,EACrBoD,wBAAwB,EACO;IAEjC,MAAME,WAAW;IAEjB,2EAA2E;IAC3E,+DAA+D;IAC/D,IAAIH,sBAAsB,cAAcF,cAAc;QACpD,MAAMA,aAAaQ,QAAQ;IAC7B;IAEA,OAAOZ,kBAAkBI,cAAc;QACrC,qDAAqD;QACrD3D;QAEA,gCAAgC;QAChCU,yBAAyB,CAACoD,2BACtBrD,yBAAyBC,yBACzB;QAEJ,+EAA+E;QAC/EkD,oBAAoB1B,4BAA4B0B,qBAAqB;QAErE,kDAAkD;QAClDrB,uBAAuByB;KACxB;AACH"}