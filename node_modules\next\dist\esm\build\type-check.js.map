{"version": 3, "sources": ["../../src/build/type-check.ts"], "names": ["path", "Log", "Worker", "JestWorker", "verifyAndLint", "createSpinner", "eventTypeCheckCompleted", "isError", "verifyTypeScriptSetup", "dir", "distDir", "intentDirs", "typeCheckPreflight", "tsconfigPath", "disableStaticImages", "cacheDir", "enableWorkerThreads", "hasAppDir", "hasPagesDir", "typeCheckWorker", "require", "resolve", "numWorkers", "maxRetries", "getStdout", "pipe", "process", "stdout", "getStderr", "stderr", "then", "result", "end", "catch", "exit", "startTypeChecking", "config", "ignoreESLint", "nextBuildSpan", "pagesDir", "runLint", "shouldLint", "telemetry", "appDir", "ignoreTypeScriptErrors", "Boolean", "typescript", "ignoreBuildErrors", "eslintCacheDir", "join", "info", "typeCheckingAndLintingSpinnerPrefixText", "typeCheckingAndLintingSpinner", "typeCheckStart", "hrtime", "verifyResult", "typeCheckEnd", "Promise", "all", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "filter", "images", "experimental", "workerThreads", "resolved", "checkEnd", "eslint", "dirs", "stopAndPersist", "record", "durationInSeconds", "typescriptVersion", "version", "inputFilesCount", "totalFilesCount", "incremental", "err", "message", "flush"], "mappings": "AAIA,OAAOA,UAAU,OAAM;AACvB,YAAYC,SAAS,eAAc;AACnC,SAASC,UAAUC,UAAU,QAAQ,iCAAgC;AACrE,SAASC,aAAa,QAAQ,uBAAsB;AACpD,OAAOC,mBAAmB,YAAW;AACrC,SAASC,uBAAuB,QAAQ,sBAAqB;AAC7D,OAAOC,aAAa,kBAAiB;AAErC;;;;;;;CAOC,GACD,SAASC,sBACPC,GAAW,EACXC,OAAe,EACfC,UAAoB,EACpBC,kBAA2B,EAC3BC,YAAoB,EACpBC,mBAA4B,EAC5BC,QAA4B,EAC5BC,mBAAwC,EACxCC,SAAkB,EAClBC,WAAoB;IAEpB,MAAMC,kBAAkB,IAAIhB,WAC1BiB,QAAQC,OAAO,CAAC,mCAChB;QACEC,YAAY;QACZN;QACAO,YAAY;IACd;IAKFJ,gBAAgBK,SAAS,GAAGC,IAAI,CAACC,QAAQC,MAAM;IAC/CR,gBAAgBS,SAAS,GAAGH,IAAI,CAACC,QAAQG,MAAM;IAE/C,OAAOV,gBACJX,qBAAqB,CAAC;QACrBC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAE;QACAC;IACF,GACCY,IAAI,CAAC,CAACC;QACLZ,gBAAgBa,GAAG;QACnB,OAAOD;IACT,GACCE,KAAK,CAAC;QACL,2FAA2F;QAC3F,8FAA8F;QAC9FP,QAAQQ,IAAI,CAAC;IACf;AACJ;AAEA,OAAO,eAAeC,kBAAkB,EACtCpB,QAAQ,EACRqB,MAAM,EACN3B,GAAG,EACH4B,YAAY,EACZC,aAAa,EACbC,QAAQ,EACRC,OAAO,EACPC,UAAU,EACVC,SAAS,EACTC,MAAM,EAYP;IACC,MAAMC,yBAAyBC,QAAQT,OAAOU,UAAU,CAACC,iBAAiB;IAE1E,MAAMC,iBAAiBhD,KAAKiD,IAAI,CAAClC,UAAU;IAE3C,IAAI6B,wBAAwB;QAC1B3C,IAAIiD,IAAI,CAAC;IACX;IACA,IAAIV,WAAWH,cAAc;QAC3B,uEAAuE;QACvEpC,IAAIiD,IAAI,CAAC;IACX;IAEA,IAAIC;IACJ,IAAIC;IAIJ,IAAI,CAACR,0BAA0BH,YAAY;QACzCU,0CACE;IACJ,OAAO,IAAI,CAACP,wBAAwB;QAClCO,0CAA0C;IAC5C,OAAO,IAAIV,YAAY;QACrBU,0CAA0C;IAC5C;IAEA,mFAAmF;IACnF,4EAA4E;IAC5E,IAAIA,yCAAyC;QAC3CC,gCAAgC/C,cAC9B8C;IAEJ;IAEA,MAAME,iBAAiB3B,QAAQ4B,MAAM;IAErC,IAAI;QACF,MAAM,CAAC,CAACC,cAAcC,aAAa,CAAC,GAAG,MAAMC,QAAQC,GAAG,CAAC;YACvDpB,cAAcqB,UAAU,CAAC,2BAA2BC,YAAY,CAAC,IAC/DpD,sBACEC,KACA2B,OAAO1B,OAAO,EACd;oBAAC6B;oBAAUI;iBAAO,CAACkB,MAAM,CAAChB,UAC1B,CAACD,wBACDR,OAAOU,UAAU,CAACjC,YAAY,EAC9BuB,OAAO0B,MAAM,CAAChD,mBAAmB,EACjCC,UACAqB,OAAO2B,YAAY,CAACC,aAAa,EACjC,CAAC,CAACrB,QACF,CAAC,CAACJ,UACFT,IAAI,CAAC,CAACmC;oBACN,MAAMC,WAAWxC,QAAQ4B,MAAM,CAACD;oBAChC,OAAO;wBAACY;wBAAUC;qBAAS;gBAC7B;YAEFzB,cACEH,cAAcqB,UAAU,CAAC,mBAAmBC,YAAY,CAAC;oBAIrDxB;gBAHF,MAAMhC,cACJK,KACAuC,iBACAZ,iBAAAA,OAAO+B,MAAM,qBAAb/B,eAAegC,IAAI,EACnBhC,OAAO2B,YAAY,CAACC,aAAa,EACjCtB;YAEJ;SACH;QACDU,iDAAAA,8BAA+BiB,cAAc;QAE7C,IAAI,CAACzB,0BAA0BW,cAAc;gBAKtBA,sBACAA,uBACJA;YANjBb,UAAU4B,MAAM,CACdhE,wBAAwB;gBACtBiE,mBAAmBf,YAAY,CAAC,EAAE;gBAClCgB,mBAAmBjB,aAAakB,OAAO;gBACvCC,eAAe,GAAEnB,uBAAAA,aAAaxB,MAAM,qBAAnBwB,qBAAqBmB,eAAe;gBACrDC,eAAe,GAAEpB,wBAAAA,aAAaxB,MAAM,qBAAnBwB,sBAAqBoB,eAAe;gBACrDC,WAAW,GAAErB,wBAAAA,aAAaxB,MAAM,qBAAnBwB,sBAAqBqB,WAAW;YAC/C;QAEJ;IACF,EAAE,OAAOC,KAAK;QACZ,mDAAmD;QACnD,8CAA8C;QAC9C,IAAItE,QAAQsE,QAAQA,IAAIC,OAAO,KAAK,8BAA8B;YAChE,MAAMpC,UAAUqC,KAAK;YACrBrD,QAAQQ,IAAI,CAAC;QACf;QACA,MAAM2C;IACR;AACF"}