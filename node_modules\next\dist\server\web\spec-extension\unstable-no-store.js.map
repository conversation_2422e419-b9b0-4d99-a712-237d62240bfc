{"version": 3, "sources": ["../../../../src/server/web/spec-extension/unstable-no-store.ts"], "names": ["unstable_noStore", "staticGenerationStore", "staticGenerationAsyncStorage", "getStore", "isUnstableCacheCallback", "staticGenerationBailout", "link"], "mappings": ";;;;+BAGgBA;;;eAAAA;;;sDAH6B;yCACL;AAEjC,SAASA;IACd,MAAMC,wBAAwBC,kEAA4B,CAACC,QAAQ;IAEnE,IAAIF,yCAAAA,sBAAuBG,uBAAuB,EAAE;QAClD,kEAAkE;QAClE,sEAAsE;QACtE;IACF;IAEAC,IAAAA,gDAAuB,EAAC,oBAAoB;QAC1CC,MAAM;IACR;AACF"}