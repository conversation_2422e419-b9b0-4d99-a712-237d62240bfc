{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/server-patch-reducer.ts"], "names": ["createHrefFromUrl", "applyRouterStatePatchToTree", "isNavigatingToNewRootLayout", "handleExternalUrl", "applyFlightData", "handleMutable", "createEmptyCacheNode", "serverPatchReducer", "state", "action", "flightData", "overrideCanonicalUrl", "mutable", "preserveCustomHistoryState", "pushRef", "pendingPush", "currentTree", "tree", "currentCache", "cache", "flightDataPath", "flightSegmentPath", "slice", "treePatch", "newTree", "Error", "canonicalUrl", "canonicalUrlOverrideHref", "undefined", "patchedTree"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,0BAAyB;AAC3D,SAASC,2BAA2B,QAAQ,sCAAqC;AACjF,SAASC,2BAA2B,QAAQ,sCAAqC;AAOjF,SAASC,iBAAiB,QAAQ,qBAAoB;AACtD,SAASC,eAAe,QAAQ,uBAAsB;AACtD,SAASC,aAAa,QAAQ,oBAAmB;AAEjD,SAASC,oBAAoB,QAAQ,mBAAkB;AAEvD,OAAO,SAASC,mBACdC,KAA2B,EAC3BC,MAAyB;IAEzB,MAAM,EAAEC,UAAU,EAAEC,oBAAoB,EAAE,GAAGF;IAE7C,MAAMG,UAAmB,CAAC;IAE1BA,QAAQC,0BAA0B,GAAG;IAErC,4DAA4D;IAC5D,IAAI,OAAOH,eAAe,UAAU;QAClC,OAAOP,kBACLK,OACAI,SACAF,YACAF,MAAMM,OAAO,CAACC,WAAW;IAE7B;IAEA,IAAIC,cAAcR,MAAMS,IAAI;IAC5B,IAAIC,eAAeV,MAAMW,KAAK;IAE9B,KAAK,MAAMC,kBAAkBV,WAAY;QACvC,mFAAmF;QACnF,MAAMW,oBAAoBD,eAAeE,KAAK,CAAC,GAAG,CAAC;QAEnD,MAAM,CAACC,UAAU,GAAGH,eAAeE,KAAK,CAAC,CAAC,GAAG,CAAC;QAC9C,MAAME,UAAUvB,4BACd,sBAAsB;QACtB;YAAC;eAAOoB;SAAkB,EAC1BL,aACAO;QAGF,IAAIC,YAAY,MAAM;YACpB,MAAM,IAAIC,MAAM;QAClB;QAEA,IAAIvB,4BAA4Bc,aAAaQ,UAAU;YACrD,OAAOrB,kBACLK,OACAI,SACAJ,MAAMkB,YAAY,EAClBlB,MAAMM,OAAO,CAACC,WAAW;QAE7B;QAEA,MAAMY,2BAA2BhB,uBAC7BX,kBAAkBW,wBAClBiB;QAEJ,IAAID,0BAA0B;YAC5Bf,QAAQc,YAAY,GAAGC;QACzB;QAEA,MAAMR,QAAmBb;QACzBF,gBAAgBc,cAAcC,OAAOC;QAErCR,QAAQiB,WAAW,GAAGL;QACtBZ,QAAQO,KAAK,GAAGA;QAEhBD,eAAeC;QACfH,cAAcQ;IAChB;IAEA,OAAOnB,cAAcG,OAAOI;AAC9B"}