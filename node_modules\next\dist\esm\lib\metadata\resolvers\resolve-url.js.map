{"version": 3, "sources": ["../../../../src/lib/metadata/resolvers/resolve-url.ts"], "names": ["path", "Log", "isStringOrURL", "icon", "URL", "createLocalMetadataBase", "process", "env", "PORT", "getSocialImageFallbackMetadataBase", "metadataBase", "isMetadataBaseMissing", "defaultMetadataBase", "deploymentUrl", "VERCEL_URL", "fallbackMetadata", "NODE_ENV", "VERCEL_ENV", "warnOnce", "origin", "resolveUrl", "url", "parsedUrl", "basePath", "pathname", "joinedPath", "posix", "join", "resolveRelativeUrl", "startsWith", "resolve", "resolveAbsoluteUrlWithPathname", "result", "toString"], "mappings": "AAAA,OAAOA,UAAU,sCAAqC;AACtD,YAAYC,SAAS,4BAA2B;AAEhD,SAASC,cAAcC,IAAS;IAC9B,OAAO,OAAOA,SAAS,YAAYA,gBAAgBC;AACrD;AAEA,SAASC;IACP,OAAO,IAAID,IAAI,CAAC,iBAAiB,EAAEE,QAAQC,GAAG,CAACC,IAAI,IAAI,KAAK,CAAC;AAC/D;AAEA,uFAAuF;AACvF,qDAAqD;AACrD,OAAO,SAASC,mCACdC,YAAwB;IAExB,MAAMC,wBAAwB,CAACD;IAC/B,MAAME,sBAAsBP;IAC5B,MAAMQ,gBACJP,QAAQC,GAAG,CAACO,UAAU,IAAI,IAAIV,IAAI,CAAC,QAAQ,EAAEE,QAAQC,GAAG,CAACO,UAAU,CAAC,CAAC;IAEvE,IAAIC;IACJ,IAAIT,QAAQC,GAAG,CAACS,QAAQ,KAAK,eAAe;QAC1CD,mBAAmBH;IACrB,OAAO;QACLG,mBACET,QAAQC,GAAG,CAACS,QAAQ,KAAK,gBACzBH,iBACAP,QAAQC,GAAG,CAACU,UAAU,KAAK,YACvBJ,gBACAH,gBAAgBG,iBAAiBD;IACzC;IAEA,IAAID,uBAAuB;QACzBV,IAAIiB,QAAQ,CAAC;QACbjB,IAAIiB,QAAQ,CACV,CAAC,2FAA2F,EAAEH,iBAAiBI,MAAM,CAAC,yFAAyF,CAAC;IAEpN;IAEA,OAAOJ;AACT;AAQA,SAASK,WACPC,GAAoC,EACpCX,YAAwB;IAExB,IAAIW,eAAejB,KAAK,OAAOiB;IAC/B,IAAI,CAACA,KAAK,OAAO;IAEjB,IAAI;QACF,mEAAmE;QACnE,MAAMC,YAAY,IAAIlB,IAAIiB;QAC1B,OAAOC;IACT,EAAE,OAAM,CAAC;IAET,IAAI,CAACZ,cAAc;QACjBA,eAAeL;IACjB;IAEA,oCAAoC;IACpC,MAAMkB,WAAWb,aAAac,QAAQ,IAAI;IAC1C,MAAMC,aAAazB,KAAK0B,KAAK,CAACC,IAAI,CAACJ,UAAUF;IAE7C,OAAO,IAAIjB,IAAIqB,YAAYf;AAC7B;AAEA,uDAAuD;AACvD,SAASkB,mBAAmBP,GAAiB,EAAEG,QAAgB;IAC7D,IAAI,OAAOH,QAAQ,YAAYA,IAAIQ,UAAU,CAAC,OAAO;QACnD,OAAO7B,KAAK0B,KAAK,CAACI,OAAO,CAACN,UAAUH;IACtC;IACA,OAAOA;AACT;AAEA,kFAAkF;AAClF,SAASU,+BACPV,GAAiB,EACjBX,YAAwB,EACxBc,QAAgB;IAEhBH,MAAMO,mBAAmBP,KAAKG;IAE9B,MAAMQ,SAAStB,eAAeU,WAAWC,KAAKX,gBAAgBW;IAC9D,OAAOW,OAAOC,QAAQ;AACxB;AAEA,SACE/B,aAAa,EACbkB,UAAU,EACVQ,kBAAkB,EAClBG,8BAA8B,KAC/B"}