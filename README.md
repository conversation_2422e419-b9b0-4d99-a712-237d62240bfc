# Suziren Digital Avatar - 明星数字人视频聊天工具

基于Live2D和WebRTC技术的明星数字人视频聊天工具网页版，提供沉浸式的AI数字人交互体验。

## ✨ 功能特性

### 核心功能
- 🎭 **Live2D数字人渲染** - 高质量的2D数字人模型显示和动画
- 📹 **WebRTC视频通信** - 实时视频流处理和P2P通信
- 🤖 **AI智能对话** - 集成OpenAI GPT的智能对话系统
- 🎤 **语音交互** - 语音识别(STT)和语音合成(TTS)
- 😊 **实时表情同步** - 基于面部识别的表情实时同步
- 🎯 **手势识别** - 点头、摇头、眨眼等手势识别
- 💬 **多模态交互** - 文本、语音、视觉多种交互方式

### 高级功能
- 🎨 **多种数字人模型** - 支持不同风格的数字人角色
- ⚡ **性能优化** - 智能性能监控和自动优化
- 🔧 **兼容性测试** - 全面的浏览器和设备兼容性检测
- 📊 **实时监控** - 性能指标实时监控和分析
- 🎛️ **个性化设置** - 丰富的个性化配置选项

## 🚀 技术栈

- **前端框架**: Next.js 14 + React 18 + TypeScript
- **样式**: Tailwind CSS
- **3D渲染**: Live2D Cubism SDK + WebGL
- **视频处理**: WebRTC + MediaDevices API
- **面部识别**: MediaPipe / face-api.js
- **AI对话**: OpenAI GPT API
- **语音处理**: Web Speech API
- **状态管理**: React Hooks

## 📦 项目结构

```
suziren-digital-avatar/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── globals.css        # 全局样式
│   │   ├── layout.tsx         # 根布局
│   │   └── page.tsx           # 主页面
│   ├── components/            # React组件
│   │   ├── Header.tsx         # 头部组件
│   │   ├── Live2DViewer.tsx   # Live2D查看器
│   │   ├── ChatPanel.tsx      # 聊天面板
│   │   ├── ControlPanel.tsx   # 控制面板
│   │   ├── VideoChat.tsx      # 视频聊天
│   │   └── LoadingScreen.tsx  # 加载屏幕
│   ├── utils/                 # 工具函数
│   │   ├── live2d.ts         # Live2D管理器
│   │   ├── ai-chat.ts        # AI聊天工具
│   │   ├── speech.ts         # 语音处理
│   │   └── face-detection.ts # 面部识别
│   └── types/                 # TypeScript类型定义
│       └── index.ts
├── public/                    # 静态资源
│   └── models/               # Live2D模型文件
├── package.json
├── next.config.js
├── tailwind.config.js
└── tsconfig.json
```

## 🛠️ 安装和运行

### 前置要求

- Node.js 18+ 
- npm 或 yarn
- 现代浏览器（支持WebGL和WebRTC）

### 安装依赖

```bash
npm install
# 或
yarn install
```

### 环境配置

创建 `.env.local` 文件：

```env
NEXT_PUBLIC_OPENAI_API_KEY=your_openai_api_key_here
```

### 开发模式

```bash
npm run dev
# 或
yarn dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

### 生产构建

```bash
npm run build
npm start
# 或
yarn build
yarn start
```

## 🎮 使用说明

### 基本操作

1. **启动应用** - 打开浏览器访问应用地址
2. **开启摄像头** - 点击控制面板中的"开启摄像头"按钮
3. **选择模型** - 在下拉菜单中选择喜欢的数字人模型
4. **开始聊天** - 在聊天面板中输入文字或使用语音输入

### 高级功能

- **表情同步** - 开启摄像头后，数字人会根据你的表情实时变化
- **语音对话** - 点击麦克风按钮进行语音输入，AI会语音回复
- **手动表情** - 点击数字人或使用表情快捷按钮切换表情
- **截图功能** - 保存当前数字人状态的截图

## 🔧 配置选项

### Live2D模型配置

在 `src/types/index.ts` 中的 `DEFAULT_MODELS` 数组中添加新模型：

```typescript
{
  id: 'your-model-id',
  name: '模型名称',
  description: '模型描述',
  modelPath: '/models/your-model/model.model3.json',
  texturesPath: ['/models/your-model/textures/'],
  motionsPath: ['/models/your-model/motions/'],
  expressionsPath: ['/models/your-model/expressions/'],
  thumbnail: '/models/your-model/thumbnail.png'
}
```

### AI聊天配置

在 `src/utils/ai-chat.ts` 中修改 `DEFAULT_AI_CONFIG`：

```typescript
export const DEFAULT_AI_CONFIG: AIConfig = {
  apiKey: process.env.NEXT_PUBLIC_OPENAI_API_KEY || '',
  model: 'gpt-3.5-turbo',
  temperature: 0.7,
  maxTokens: 500,
  systemPrompt: '你的系统提示词...'
}
```

## 🚧 开发路线图

- [x] 基础项目架构
- [x] Live2D模型集成框架
- [x] WebRTC视频聊天
- [x] AI聊天集成
- [x] 语音处理功能
- [ ] 真实Live2D SDK集成
- [ ] 高精度面部识别
- [ ] 多人视频聊天
- [ ] 自定义模型上传
- [ ] 云端模型库
- [ ] 移动端优化

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [Live2D Cubism SDK](https://www.live2d.com/) - 数字人渲染技术
- [OpenAI](https://openai.com/) - AI对话能力
- [Next.js](https://nextjs.org/) - React框架
- [Tailwind CSS](https://tailwindcss.com/) - CSS框架

## 📞 联系我们

- 项目主页: [GitHub Repository](https://github.com/your-username/suziren-digital-avatar)
- 问题反馈: [Issues](https://github.com/your-username/suziren-digital-avatar/issues)
- 邮箱: <EMAIL>

---

⭐ 如果这个项目对你有帮助，请给我们一个星标！
