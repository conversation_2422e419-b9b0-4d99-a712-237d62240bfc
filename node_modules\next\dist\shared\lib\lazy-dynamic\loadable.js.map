{"version": 3, "sources": ["../../../../src/shared/lib/lazy-dynamic/loadable.tsx"], "names": ["Loadable", "options", "opts", "Object", "assign", "loader", "loading", "ssr", "lazy", "React", "LoadableComponent", "props", "Loading", "fallbackElement", "isLoading", "past<PERSON>elay", "error", "Wrap", "Fragment", "NoSSR", "Lazy", "Suspense", "fallback", "displayName"], "mappings": ";;;;+BAsCA;;;eAAA;;;;gEAtCkB;8BACI;AAEtB,SAASA,SAASC,OAAY;IAC5B,MAAMC,OAAOC,OAAOC,MAAM,CACxB;QACEC,QAAQ;QACRC,SAAS;QACTC,KAAK;IACP,GACAN;IAGFC,KAAKM,IAAI,iBAAGC,cAAK,CAACD,IAAI,CAACN,KAAKG,MAAM;IAElC,SAASK,kBAAkBC,KAAU;QACnC,MAAMC,UAAUV,KAAKI,OAAO;QAC5B,MAAMO,gCACJ,6BAACD;YAAQE,WAAW;YAAMC,WAAW;YAAMC,OAAO;;QAGpD,MAAMC,OAAOf,KAAKK,GAAG,GAAGE,cAAK,CAACS,QAAQ,GAAGC,mBAAK;QAC9C,MAAMC,OAAOlB,KAAKM,IAAI;QAEtB,qBACE,6BAACC,cAAK,CAACY,QAAQ;YAACC,UAAUT;yBACxB,6BAACI,0BACC,6BAACG,MAAST;IAIlB;IAEAD,kBAAkBa,WAAW,GAAG;IAEhC,OAAOb;AACT;MAEA,WAAeV"}