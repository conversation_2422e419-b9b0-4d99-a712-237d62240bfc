// WebRTC 管理器
import { WebRTCConfig } from '@/types'

/**
 * WebRTC 连接管理器
 */
export class WebRTCManager {
  private peerConnection: RTCPeerConnection | null = null
  private localStream: MediaStream | null = null
  private remoteStream: MediaStream | null = null
  private config: WebRTCConfig
  private isConnected: boolean = false
  private onRemoteStreamCallback: ((stream: MediaStream) => void) | null = null
  private onConnectionStateCallback: ((state: string) => void) | null = null

  constructor(config: WebRTCConfig) {
    this.config = config
  }

  /**
   * 初始化WebRTC连接
   */
  async initialize(): Promise<void> {
    try {
      // 创建RTCPeerConnection
      this.peerConnection = new RTCPeerConnection({
        iceServers: this.config.iceServers
      })

      // 设置事件监听器
      this.setupEventListeners()

      console.log('WebRTC Manager initialized')
    } catch (error) {
      console.error('Failed to initialize WebRTC:', error)
      throw error
    }
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    if (!this.peerConnection) return

    // ICE候选事件
    this.peerConnection.onicecandidate = (event) => {
      if (event.candidate) {
        console.log('New ICE candidate:', event.candidate)
        // 在实际应用中，这里会通过信令服务器发送候选信息
      }
    }

    // 连接状态变化
    this.peerConnection.onconnectionstatechange = () => {
      const state = this.peerConnection?.connectionState || 'unknown'
      console.log('Connection state changed:', state)
      this.isConnected = state === 'connected'
      this.onConnectionStateCallback?.(state)
    }

    // 远程流接收
    this.peerConnection.ontrack = (event) => {
      console.log('Received remote stream')
      this.remoteStream = event.streams[0]
      this.onRemoteStreamCallback?.(this.remoteStream)
    }

    // ICE连接状态变化
    this.peerConnection.oniceconnectionstatechange = () => {
      console.log('ICE connection state:', this.peerConnection?.iceConnectionState)
    }
  }

  /**
   * 获取本地媒体流
   */
  async getLocalStream(): Promise<MediaStream> {
    try {
      this.localStream = await navigator.mediaDevices.getUserMedia({
        video: this.config.video,
        audio: this.config.audio
      })

      console.log('Local stream obtained')
      return this.localStream
    } catch (error) {
      console.error('Failed to get local stream:', error)
      throw error
    }
  }

  /**
   * 添加本地流到连接
   */
  addLocalStream(stream: MediaStream): void {
    if (!this.peerConnection) {
      throw new Error('Peer connection not initialized')
    }

    stream.getTracks().forEach(track => {
      this.peerConnection!.addTrack(track, stream)
    })

    console.log('Local stream added to peer connection')
  }

  /**
   * 创建Offer
   */
  async createOffer(): Promise<RTCSessionDescriptionInit> {
    if (!this.peerConnection) {
      throw new Error('Peer connection not initialized')
    }

    try {
      const offer = await this.peerConnection.createOffer()
      await this.peerConnection.setLocalDescription(offer)
      
      console.log('Offer created and set as local description')
      return offer
    } catch (error) {
      console.error('Failed to create offer:', error)
      throw error
    }
  }

  /**
   * 创建Answer
   */
  async createAnswer(offer: RTCSessionDescriptionInit): Promise<RTCSessionDescriptionInit> {
    if (!this.peerConnection) {
      throw new Error('Peer connection not initialized')
    }

    try {
      await this.peerConnection.setRemoteDescription(offer)
      const answer = await this.peerConnection.createAnswer()
      await this.peerConnection.setLocalDescription(answer)
      
      console.log('Answer created and set as local description')
      return answer
    } catch (error) {
      console.error('Failed to create answer:', error)
      throw error
    }
  }

  /**
   * 设置远程描述
   */
  async setRemoteDescription(description: RTCSessionDescriptionInit): Promise<void> {
    if (!this.peerConnection) {
      throw new Error('Peer connection not initialized')
    }

    try {
      await this.peerConnection.setRemoteDescription(description)
      console.log('Remote description set')
    } catch (error) {
      console.error('Failed to set remote description:', error)
      throw error
    }
  }

  /**
   * 添加ICE候选
   */
  async addIceCandidate(candidate: RTCIceCandidateInit): Promise<void> {
    if (!this.peerConnection) {
      throw new Error('Peer connection not initialized')
    }

    try {
      await this.peerConnection.addIceCandidate(candidate)
      console.log('ICE candidate added')
    } catch (error) {
      console.error('Failed to add ICE candidate:', error)
      throw error
    }
  }

  /**
   * 设置远程流回调
   */
  setOnRemoteStream(callback: (stream: MediaStream) => void): void {
    this.onRemoteStreamCallback = callback
  }

  /**
   * 设置连接状态回调
   */
  setOnConnectionState(callback: (state: string) => void): void {
    this.onConnectionStateCallback = callback
  }

  /**
   * 获取连接状态
   */
  getConnectionState(): string {
    return this.peerConnection?.connectionState || 'unknown'
  }

  /**
   * 检查是否已连接
   */
  isConnectionActive(): boolean {
    return this.isConnected
  }

  /**
   * 获取本地流
   */
  getLocalStreamInstance(): MediaStream | null {
    return this.localStream
  }

  /**
   * 获取远程流
   */
  getRemoteStreamInstance(): MediaStream | null {
    return this.remoteStream
  }

  /**
   * 关闭连接
   */
  close(): void {
    // 停止本地流
    if (this.localStream) {
      this.localStream.getTracks().forEach(track => track.stop())
      this.localStream = null
    }

    // 关闭peer connection
    if (this.peerConnection) {
      this.peerConnection.close()
      this.peerConnection = null
    }

    this.remoteStream = null
    this.isConnected = false
    
    console.log('WebRTC connection closed')
  }

  /**
   * 释放资源
   */
  dispose(): void {
    this.close()
    this.onRemoteStreamCallback = null
    this.onConnectionStateCallback = null
  }
}

/**
 * 默认WebRTC配置
 */
export const DEFAULT_WEBRTC_CONFIG: WebRTCConfig = {
  iceServers: [
    { urls: 'stun:stun.l.google.com:19302' },
    { urls: 'stun:stun1.l.google.com:19302' }
  ],
  video: {
    width: { ideal: 640 },
    height: { ideal: 480 },
    frameRate: { ideal: 30 },
    facingMode: 'user'
  },
  audio: {
    echoCancellation: true,
    noiseSuppression: true,
    autoGainControl: true
  }
}

/**
 * 媒体设备管理器
 */
export class MediaDeviceManager {
  /**
   * 获取可用的视频设备
   */
  static async getVideoDevices(): Promise<MediaDeviceInfo[]> {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices()
      return devices.filter(device => device.kind === 'videoinput')
    } catch (error) {
      console.error('Failed to get video devices:', error)
      return []
    }
  }

  /**
   * 获取可用的音频设备
   */
  static async getAudioDevices(): Promise<MediaDeviceInfo[]> {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices()
      return devices.filter(device => device.kind === 'audioinput')
    } catch (error) {
      console.error('Failed to get audio devices:', error)
      return []
    }
  }

  /**
   * 检查设备权限
   */
  static async checkPermissions(): Promise<{
    camera: boolean
    microphone: boolean
  }> {
    try {
      const cameraPermission = await navigator.permissions.query({ name: 'camera' as PermissionName })
      const microphonePermission = await navigator.permissions.query({ name: 'microphone' as PermissionName })

      return {
        camera: cameraPermission.state === 'granted',
        microphone: microphonePermission.state === 'granted'
      }
    } catch (error) {
      console.error('Failed to check permissions:', error)
      return { camera: false, microphone: false }
    }
  }

  /**
   * 请求设备权限
   */
  static async requestPermissions(): Promise<MediaStream | null> {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: true,
        audio: true
      })
      
      // 立即停止流，只是为了获取权限
      stream.getTracks().forEach(track => track.stop())
      
      return stream
    } catch (error) {
      console.error('Failed to request permissions:', error)
      return null
    }
  }
}

/**
 * 创建WebRTC管理器
 */
export const createWebRTCManager = (config: WebRTCConfig = DEFAULT_WEBRTC_CONFIG) => {
  return new WebRTCManager(config)
}
