{"version": 3, "sources": ["../../../../src/server/web/spec-extension/revalidate-tag.ts"], "names": ["staticGenerationBailout", "revalidateTag", "tag", "staticGenerationAsyncStorage", "fetch", "__nextGetStaticStore", "store", "getStore", "incrementalCache", "Error", "revalidatedTags", "includes", "push", "pendingRevalidates", "catch", "err", "console", "error", "pathWasRevalidated"], "mappings": "AAIA,SAASA,uBAAuB,QAAQ,uDAAsD;AAE9F,OAAO,SAASC,cAAcC,GAAW;IACvC,MAAMC,+BAA+B,AACnCC,MACAC,oBAAoB,oBAFe,AACnCD,MACAC,oBAAoB,MADpBD;IAGF,MAAME,QACJH,gDAAAA,6BAA8BI,QAAQ;IAExC,IAAI,CAACD,SAAS,CAACA,MAAME,gBAAgB,EAAE;QACrC,MAAM,IAAIC,MACR,CAAC,4DAA4D,EAAEP,IAAI,CAAC;IAExE;IAEA,2EAA2E;IAC3E,oDAAoD;IACpDF,wBAAwB,CAAC,cAAc,EAAEE,IAAI,CAAC;IAE9C,IAAI,CAACI,MAAMI,eAAe,EAAE;QAC1BJ,MAAMI,eAAe,GAAG,EAAE;IAC5B;IACA,IAAI,CAACJ,MAAMI,eAAe,CAACC,QAAQ,CAACT,MAAM;QACxCI,MAAMI,eAAe,CAACE,IAAI,CAACV;IAC7B;IAEA,IAAI,CAACI,MAAMO,kBAAkB,EAAE;QAC7BP,MAAMO,kBAAkB,GAAG,CAAC;IAC9B;IACAP,MAAMO,kBAAkB,CAACX,IAAI,GAAGI,MAAME,gBAAgB,CACnDP,aAAa,oBADgBK,MAAME,gBAAgB,CACnDP,aAAa,MADgBK,MAAME,gBAAgB,EACnCN,KAChBY,KAAK,CAAC,CAACC;QACNC,QAAQC,KAAK,CAAC,CAAC,yBAAyB,EAAEf,IAAI,CAAC,EAAEa;IACnD;IAEF,4CAA4C;IAC5CT,MAAMY,kBAAkB,GAAG;AAC7B"}