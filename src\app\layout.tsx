import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Suziren Digital Avatar - 明星数字人视频聊天',
  description: '基于Live2D技术的明星数字人实时视频聊天工具，支持AI对话、表情同步和语音交互',
  keywords: 'Live2D, 数字人, AI聊天, 视频通话, VTuber, 虚拟主播',
  authors: [{ name: 'Suziren Team' }],
  viewport: 'width=device-width, initial-scale=1',
  themeColor: '#0ea5e9',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-CN">
      <head>
        <link rel="icon" href="/favicon.ico" />
        <meta name="theme-color" content="#0ea5e9" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="Suziren Digital Avatar" />
      </head>
      <body className={inter.className}>
        <div id="root" className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
          {children}
        </div>
      </body>
    </html>
  )
}
