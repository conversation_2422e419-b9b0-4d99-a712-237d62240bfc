{"version": 3, "sources": ["../../src/lib/patch-incorrect-lockfile.ts"], "names": ["patchIncorrectLockfile", "registry", "fetchPkgInfo", "pkg", "getRegistry", "res", "fetch", "ok", "Error", "status", "data", "json", "versionData", "versions", "nextPkgJson", "version", "os", "cpu", "engines", "tarball", "dist", "integrity", "dir", "process", "env", "NEXT_IGNORE_INCORRECT_LOCKFILE", "lockfilePath", "findUp", "cwd", "content", "promises", "readFile", "endingNewline", "endsWith", "lockfileParsed", "JSON", "parse", "lockfileVersion", "parseInt", "expectedSwcPkgs", "Object", "keys", "patchDependency", "pkgData", "dependencies", "resolved", "optional", "patchPackage", "packages", "supportedVersions", "includes", "shouldPatchDependencies", "shouldPatchPackages", "missingSwcPkgs", "pkgPrefix", "substring", "length", "push", "Log", "warn", "isCI", "pkgsData", "Promise", "all", "map", "i", "writeFile", "stringify", "err", "error", "console"], "mappings": ";;;;+BAsCsBA;;;eAAAA;;;oBAtCG;6DACJ;+DACF;oEAEK;wBAEH;6BACO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE5B,IAAIC;AAEJ,eAAeC,aAAaC,GAAW;IACrC,IAAI,CAACF,UAAUA,WAAWG,IAAAA,wBAAW;IACrC,MAAMC,MAAM,MAAMC,MAAM,CAAC,EAAEL,SAAS,EAAEE,IAAI,CAAC;IAE3C,IAAI,CAACE,IAAIE,EAAE,EAAE;QACX,MAAM,IAAIC,MACR,CAAC,kCAAkC,EAAEL,IAAI,aAAa,EAAEE,IAAII,MAAM,CAAC,CAAC;IAExE;IACA,MAAMC,OAAO,MAAML,IAAIM,IAAI;IAC3B,MAAMC,cAAcF,KAAKG,QAAQ,CAACC,oBAAW,CAACC,OAAO,CAAC;IAEtD,OAAO;QACLC,IAAIJ,YAAYI,EAAE;QAClBC,KAAKL,YAAYK,GAAG;QACpBC,SAASN,YAAYM,OAAO;QAC5BC,SAASP,YAAYQ,IAAI,CAACD,OAAO;QACjCE,WAAWT,YAAYQ,IAAI,CAACC,SAAS;IACvC;AACF;AAQO,eAAerB,uBAAuBsB,GAAW;IACtD,IAAIC,QAAQC,GAAG,CAACC,8BAA8B,EAAE;QAC9C;IACF;IACA,MAAMC,eAAe,MAAMC,IAAAA,eAAM,EAAC,qBAAqB;QAAEC,KAAKN;IAAI;IAElE,IAAI,CAACI,cAAc;QACjB,oDAAoD;QACpD;IACF;IACA,MAAMG,UAAU,MAAMC,YAAQ,CAACC,QAAQ,CAACL,cAAc;IACtD,+BAA+B;IAC/B,MAAMM,gBAAgBH,QAAQI,QAAQ,CAAC,UACnC,SACAJ,QAAQI,QAAQ,CAAC,QACjB,OACA;IAEJ,MAAMC,iBAAiBC,KAAKC,KAAK,CAACP;IAClC,MAAMQ,kBAAkBC,SAASJ,kCAAAA,eAAgBG,eAAe,EAAE;IAClE,MAAME,kBAAkBC,OAAOC,IAAI,CAAC3B,oBAAW,CAAC,uBAAuB,IAAI,CAAC;IAE5E,MAAM4B,kBAAkB,CACtBvC,KACAwC;QAEAT,eAAeU,YAAY,CAACzC,IAAI,GAAG;YACjCY,SAASD,oBAAW,CAACC,OAAO;YAC5B8B,UAAUF,QAAQxB,OAAO;YACzBE,WAAWsB,QAAQtB,SAAS;YAC5ByB,UAAU;QACZ;IACF;IAEA,MAAMC,eAAe,CACnB5C,KACAwC;QAEAT,eAAec,QAAQ,CAAC7C,IAAI,GAAG;YAC7BY,SAASD,oBAAW,CAACC,OAAO;YAC5B8B,UAAUF,QAAQxB,OAAO;YACzBE,WAAWsB,QAAQtB,SAAS;YAC5BJ,KAAK0B,QAAQ1B,GAAG;YAChB6B,UAAU;YACV9B,IAAI2B,QAAQ3B,EAAE;YACdE,SAASyB,QAAQzB,OAAO;QAC1B;IACF;IAEA,IAAI;QACF,MAAM+B,oBAAoB;YAAC;YAAG;YAAG;SAAE;QAEnC,IAAI,CAACA,kBAAkBC,QAAQ,CAACb,kBAAkB;YAChD,8BAA8B;YAC9B;QACF;QACA,4BAA4B;QAC5B,oCAAoC;QACpC,wBAAwB;QACxB,MAAMc,0BACJd,oBAAoB,KAAKA,oBAAoB;QAC/C,MAAMe,sBAAsBf,oBAAoB,KAAKA,oBAAoB;QAEzE,IACE,AAACc,2BAA2B,CAACjB,eAAeU,YAAY,IACvDQ,uBAAuB,CAAClB,eAAec,QAAQ,EAChD;YACA,2BAA2B;YAC3B;QACF;QACA,MAAMK,iBAAiB,EAAE;QACzB,IAAIC;QAEJ,IAAIF,qBAAqB;YACvBE,YAAY;YACZ,KAAK,MAAMnD,OAAOqC,OAAOC,IAAI,CAACP,eAAec,QAAQ,EAAG;gBACtD,IAAI7C,IAAI8B,QAAQ,CAAC,sBAAsB;oBACrCqB,YAAYnD,IAAIoD,SAAS,CAAC,GAAGpD,IAAIqD,MAAM,GAAG;gBAC5C;YACF;YAEA,IAAI,CAACF,WAAW;gBACd,4CAA4C;gBAC5C;YACF;QACF;QAEA,KAAK,MAAMnD,OAAOoC,gBAAiB;YACjC,IACE,AAACY,2BAA2B,CAACjB,eAAeU,YAAY,CAACzC,IAAI,IAC5DiD,uBAAuB,CAAClB,eAAec,QAAQ,CAAC,CAAC,EAAEM,UAAU,EAAEnD,IAAI,CAAC,CAAC,EACtE;gBACAkD,eAAeI,IAAI,CAACtD;YACtB;QACF;QACA,IAAIkD,eAAeG,MAAM,KAAK,GAAG;YAC/B;QACF;QACAE,KAAIC,IAAI,CACN,CAAC,wCAAwC,CAAC,EAC1CC,YAAI,GAAG,4CAA4C;QAGrD,IAAIA,YAAI,EAAE;YACR,8DAA8D;YAC9D;QACF;QACA,MAAMC,WAAW,MAAMC,QAAQC,GAAG,CAChCV,eAAeW,GAAG,CAAC,CAAC7D,MAAQD,aAAaC;QAG3C,IAAK,IAAI8D,IAAI,GAAGA,IAAIJ,SAASL,MAAM,EAAES,IAAK;YACxC,MAAM9D,MAAMkD,cAAc,CAACY,EAAE;YAC7B,MAAMtB,UAAUkB,QAAQ,CAACI,EAAE;YAE3B,IAAId,yBAAyB;gBAC3BT,gBAAgBvC,KAAKwC;YACvB;YACA,IAAIS,qBAAqB;gBACvBL,aAAa,CAAC,EAAEO,UAAU,EAAEnD,IAAI,CAAC,EAAEwC;YACrC;QACF;QAEA,MAAMb,YAAQ,CAACoC,SAAS,CACtBxC,cACAS,KAAKgC,SAAS,CAACjC,gBAAgB,MAAM,KAAKF;QAE5C0B,KAAIC,IAAI,CACN;IAEJ,EAAE,OAAOS,KAAK;QACZV,KAAIW,KAAK,CACP,CAAC,yFAAyF,CAAC;QAE7FC,QAAQD,KAAK,CAACD;IAChB;AACF"}