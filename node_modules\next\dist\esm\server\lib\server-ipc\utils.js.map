{"version": 3, "sources": ["../../../../src/server/lib/server-ipc/utils.ts"], "names": ["ipcForbiddenHeaders", "actionsForbiddenHeaders", "filterReqHeaders", "headers", "forbiddenHeaders", "key", "value", "Object", "entries", "includes", "Array", "isArray"], "mappings": "AAAA,OAAO,MAAMA,sBAAsB;IACjC;IACA;IACA;IACA;IACA;IACA,+CAA+C;IAC/C;IACA,2IAA2I;IAC3I;CACD,CAAA;AAED,OAAO,MAAMC,0BAA0B;OAClCD;IACH;CACD,CAAA;AAED,OAAO,MAAME,mBAAmB,CAC9BC,SACAC;IAEA,kGAAkG;IAClG,+CAA+C;IAC/C,IAAID,OAAO,CAAC,iBAAiB,IAAIA,OAAO,CAAC,iBAAiB,KAAK,KAAK;QAClE,OAAOA,OAAO,CAAC,iBAAiB;IAClC;IAEA,KAAK,MAAM,CAACE,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAACL,SAAU;QAClD,IACEC,iBAAiBK,QAAQ,CAACJ,QAC1B,CAAEK,CAAAA,MAAMC,OAAO,CAACL,UAAU,OAAOA,UAAU,QAAO,GAClD;YACA,OAAOH,OAAO,CAACE,IAAI;QACrB;IACF;IACA,OAAOF;AACT,EAAC"}