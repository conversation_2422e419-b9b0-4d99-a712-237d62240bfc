// 性能优化工具
export class PerformanceOptimizer {
  private static instance: PerformanceOptimizer
  private observers: Map<string, PerformanceObserver> = new Map()
  private optimizations: Map<string, boolean> = new Map()
  private frameRateTarget: number = 60
  private memoryThreshold: number = 80 // 内存使用率阈值（百分比）

  private constructor() {
    this.initializeOptimizations()
  }

  static getInstance(): PerformanceOptimizer {
    if (!PerformanceOptimizer.instance) {
      PerformanceOptimizer.instance = new PerformanceOptimizer()
    }
    return PerformanceOptimizer.instance
  }

  /**
   * 初始化性能优化
   */
  private initializeOptimizations(): void {
    // 启用各种优化策略
    this.optimizations.set('imageOptimization', true)
    this.optimizations.set('memoryManagement', true)
    this.optimizations.set('renderOptimization', true)
    this.optimizations.set('networkOptimization', true)
    this.optimizations.set('cacheOptimization', true)

    // 设置性能观察器
    this.setupPerformanceObservers()
  }

  /**
   * 设置性能观察器
   */
  private setupPerformanceObservers(): void {
    try {
      // 观察长任务
      if ('PerformanceObserver' in window) {
        const longTaskObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.duration > 50) { // 超过50ms的任务
              console.warn(`Long task detected: ${entry.duration}ms`)
              this.handleLongTask(entry)
            }
          }
        })
        longTaskObserver.observe({ entryTypes: ['longtask'] })
        this.observers.set('longtask', longTaskObserver)

        // 观察布局偏移
        const layoutShiftObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if ((entry as any).value > 0.1) { // CLS > 0.1
              console.warn(`Layout shift detected: ${(entry as any).value}`)
              this.handleLayoutShift(entry)
            }
          }
        })
        layoutShiftObserver.observe({ entryTypes: ['layout-shift'] })
        this.observers.set('layout-shift', layoutShiftObserver)

        // 观察最大内容绘制
        const lcpObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            console.log(`LCP: ${entry.startTime}ms`)
          }
        })
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
        this.observers.set('lcp', lcpObserver)
      }
    } catch (error) {
      console.error('Failed to setup performance observers:', error)
    }
  }

  /**
   * 处理长任务
   */
  private handleLongTask(entry: PerformanceEntry): void {
    // 自动启用更激进的优化策略
    this.enableAggressiveOptimizations()
  }

  /**
   * 处理布局偏移
   */
  private handleLayoutShift(entry: PerformanceEntry): void {
    // 优化布局稳定性
    this.optimizeLayoutStability()
  }

  /**
   * 启用激进优化策略
   */
  private enableAggressiveOptimizations(): void {
    // 降低渲染质量
    this.optimizeRenderQuality()
    
    // 减少动画复杂度
    this.reduceAnimationComplexity()
    
    // 启用内存清理
    this.enableMemoryCleanup()
  }

  /**
   * 优化渲染质量
   */
  private optimizeRenderQuality(): void {
    if (!this.optimizations.get('renderOptimization')) return

    // 动态调整Canvas分辨率
    const canvases = document.querySelectorAll('canvas')
    canvases.forEach(canvas => {
      const rect = canvas.getBoundingClientRect()
      const pixelRatio = Math.min(window.devicePixelRatio, 2) // 限制最大像素比
      
      canvas.width = rect.width * pixelRatio
      canvas.height = rect.height * pixelRatio
      
      const ctx = canvas.getContext('2d')
      if (ctx) {
        ctx.scale(pixelRatio, pixelRatio)
      }
    })
  }

  /**
   * 减少动画复杂度
   */
  private reduceAnimationComplexity(): void {
    // 禁用非关键动画
    const style = document.createElement('style')
    style.textContent = `
      * {
        animation-duration: 0.1s !important;
        animation-delay: 0s !important;
        transition-duration: 0.1s !important;
        transition-delay: 0s !important;
      }
    `
    document.head.appendChild(style)
  }

  /**
   * 启用内存清理
   */
  private enableMemoryCleanup(): void {
    if (!this.optimizations.get('memoryManagement')) return

    // 定期清理内存
    setInterval(() => {
      this.performMemoryCleanup()
    }, 30000) // 每30秒清理一次
  }

  /**
   * 执行内存清理
   */
  private performMemoryCleanup(): void {
    try {
      // 清理未使用的图片
      this.cleanupUnusedImages()
      
      // 清理事件监听器
      this.cleanupEventListeners()
      
      // 强制垃圾回收（如果可用）
      if ('gc' in window) {
        (window as any).gc()
      }
    } catch (error) {
      console.error('Memory cleanup failed:', error)
    }
  }

  /**
   * 清理未使用的图片
   */
  private cleanupUnusedImages(): void {
    const images = document.querySelectorAll('img')
    images.forEach(img => {
      if (!img.isConnected || img.offsetParent === null) {
        img.src = ''
        img.remove()
      }
    })
  }

  /**
   * 清理事件监听器
   */
  private cleanupEventListeners(): void {
    // 移除已断开连接的元素的事件监听器
    // 这需要应用程序配合实现事件监听器的跟踪
  }

  /**
   * 优化布局稳定性
   */
  private optimizeLayoutStability(): void {
    // 为图片和视频添加尺寸属性
    const media = document.querySelectorAll('img, video')
    media.forEach(element => {
      if (!element.hasAttribute('width') || !element.hasAttribute('height')) {
        const rect = element.getBoundingClientRect()
        element.setAttribute('width', rect.width.toString())
        element.setAttribute('height', rect.height.toString())
      }
    })
  }

  /**
   * 优化图片加载
   */
  optimizeImages(): void {
    if (!this.optimizations.get('imageOptimization')) return

    const images = document.querySelectorAll('img')
    images.forEach(img => {
      // 添加懒加载
      if (!img.hasAttribute('loading')) {
        img.setAttribute('loading', 'lazy')
      }

      // 添加解码提示
      if (!img.hasAttribute('decoding')) {
        img.setAttribute('decoding', 'async')
      }

      // 优化图片格式
      this.optimizeImageFormat(img)
    })
  }

  /**
   * 优化图片格式
   */
  private optimizeImageFormat(img: HTMLImageElement): void {
    const src = img.src
    if (!src) return

    // 检查浏览器支持的现代图片格式
    const supportsWebP = this.supportsImageFormat('webp')
    const supportsAVIF = this.supportsImageFormat('avif')

    if (supportsAVIF && !src.includes('.avif')) {
      // 尝试加载AVIF版本
      const avifSrc = src.replace(/\.(jpg|jpeg|png)$/i, '.avif')
      this.preloadImage(avifSrc).then(() => {
        img.src = avifSrc
      }).catch(() => {
        // AVIF不可用，尝试WebP
        if (supportsWebP) {
          const webpSrc = src.replace(/\.(jpg|jpeg|png)$/i, '.webp')
          this.preloadImage(webpSrc).then(() => {
            img.src = webpSrc
          }).catch(() => {
            // 保持原格式
          })
        }
      })
    } else if (supportsWebP && !src.includes('.webp')) {
      const webpSrc = src.replace(/\.(jpg|jpeg|png)$/i, '.webp')
      this.preloadImage(webpSrc).then(() => {
        img.src = webpSrc
      }).catch(() => {
        // 保持原格式
      })
    }
  }

  /**
   * 检查图片格式支持
   */
  private supportsImageFormat(format: string): boolean {
    const canvas = document.createElement('canvas')
    canvas.width = 1
    canvas.height = 1
    return canvas.toDataURL(`image/${format}`).indexOf(`data:image/${format}`) === 0
  }

  /**
   * 预加载图片
   */
  private preloadImage(src: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = () => resolve()
      img.onerror = () => reject()
      img.src = src
    })
  }

  /**
   * 优化网络请求
   */
  optimizeNetwork(): void {
    if (!this.optimizations.get('networkOptimization')) return

    // 启用请求合并
    this.enableRequestBatching()
    
    // 启用缓存策略
    this.enableCaching()
  }

  /**
   * 启用请求合并
   */
  private enableRequestBatching(): void {
    // 实现请求合并逻辑
    // 这需要应用程序配合实现
  }

  /**
   * 启用缓存策略
   */
  private enableCaching(): void {
    if (!this.optimizations.get('cacheOptimization')) return

    // 设置Service Worker缓存策略
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.register('/sw.js').catch(error => {
        console.error('Service Worker registration failed:', error)
      })
    }
  }

  /**
   * 获取性能建议
   */
  getPerformanceRecommendations(): string[] {
    const recommendations: string[] = []

    // 检查内存使用
    if ('memory' in performance) {
      const memory = (performance as any).memory
      const memoryUsage = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100
      
      if (memoryUsage > this.memoryThreshold) {
        recommendations.push('内存使用率过高，建议清理未使用的资源')
      }
    }

    // 检查FPS
    // 这需要外部提供FPS数据

    // 检查网络延迟
    // 这需要外部提供网络延迟数据

    return recommendations
  }

  /**
   * 设置优化选项
   */
  setOptimization(key: string, enabled: boolean): void {
    this.optimizations.set(key, enabled)
  }

  /**
   * 获取优化状态
   */
  getOptimizationStatus(): { [key: string]: boolean } {
    return Object.fromEntries(this.optimizations)
  }

  /**
   * 销毁优化器
   */
  dispose(): void {
    // 清理性能观察器
    this.observers.forEach(observer => {
      observer.disconnect()
    })
    this.observers.clear()

    // 清理优化设置
    this.optimizations.clear()
  }
}

// 导出单例实例
export const performanceOptimizer = PerformanceOptimizer.getInstance()
