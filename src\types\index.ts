// 消息类型定义
export interface Message {
  id: string
  type: 'user' | 'ai'
  content: string
  timestamp: Date
  audioUrl?: string
}

// Live2D模型相关类型
export interface Live2DModel {
  id: string
  name: string
  description: string
  modelPath: string
  texturesPath: string[]
  motionsPath: string[]
  expressionsPath: string[]
  thumbnail: string
}

// 表情类型
export interface Expression {
  id: string
  name: string
  file: string
  weight: number
}

// 动作类型
export interface Motion {
  id: string
  name: string
  file: string
  loop: boolean
  fadeInTime: number
  fadeOutTime: number
}

// 面部识别数据
export interface FaceData {
  landmarks: number[][]
  expressions: {
    happy: number
    sad: number
    angry: number
    surprised: number
    neutral: number
  }
  eyeOpenness: {
    left: number
    right: number
  }
  mouthOpenness: number
  headRotation: {
    x: number
    y: number
    z: number
  }
}

// WebRTC配置
export interface WebRTCConfig {
  iceServers: RTCIceServer[]
  video: MediaTrackConstraints
  audio: MediaTrackConstraints
}

// AI聊天配置
export interface AIConfig {
  apiKey: string
  model: string
  temperature: number
  maxTokens: number
  systemPrompt: string
}

// 语音合成配置
export interface TTSConfig {
  voice: string
  rate: number
  pitch: number
  volume: number
}

// 语音识别配置
export interface STTConfig {
  language: string
  continuous: boolean
  interimResults: boolean
}

// 应用状态
export interface AppState {
  isLoading: boolean
  isVideoEnabled: boolean
  selectedModel: string
  volume: number
  sensitivity: number
  autoExpression: boolean
  lipSync: boolean
  eyeTracking: boolean
  currentExpression: string
  isRecording: boolean
  isAISpeaking: boolean
}

// 错误类型
export interface AppError {
  code: string
  message: string
  details?: any
}

// 事件类型
export type AppEvent = 
  | { type: 'MODEL_LOADED'; payload: { modelId: string } }
  | { type: 'MODEL_ERROR'; payload: { error: AppError } }
  | { type: 'VIDEO_STARTED'; payload: {} }
  | { type: 'VIDEO_STOPPED'; payload: {} }
  | { type: 'MESSAGE_SENT'; payload: { message: Message } }
  | { type: 'MESSAGE_RECEIVED'; payload: { message: Message } }
  | { type: 'EXPRESSION_CHANGED'; payload: { expression: string } }
  | { type: 'MOTION_STARTED'; payload: { motion: string } }
  | { type: 'FACE_DETECTED'; payload: { faceData: FaceData } }
  | { type: 'RECORDING_STARTED'; payload: {} }
  | { type: 'RECORDING_STOPPED'; payload: {} }
  | { type: 'TTS_STARTED'; payload: { text: string } }
  | { type: 'TTS_FINISHED'; payload: {} }

// 组件Props类型
export interface Live2DViewerProps {
  modelId: string
  isVideoEnabled: boolean
  faceData?: FaceData
  currentExpression?: string
  onModelLoaded?: (modelId: string) => void
  onModelError?: (error: AppError) => void
}

export interface ChatPanelProps {
  messages: Message[]
  onSendMessage: (message: string) => void
  isRecording?: boolean
  isAISpeaking?: boolean
}

export interface ControlPanelProps {
  isVideoEnabled: boolean
  onVideoToggle: () => void
  selectedModel: string
  onModelChange: (modelId: string) => void
  volume: number
  onVolumeChange: (volume: number) => void
  sensitivity: number
  onSensitivityChange: (sensitivity: number) => void
}

export interface VideoChatProps {
  onFaceData?: (faceData: FaceData) => void
  isEnabled?: boolean
}

// 工具函数类型
export type EventHandler<T = any> = (event: T) => void
export type AsyncEventHandler<T = any> = (event: T) => Promise<void>

// 常量
export const DEFAULT_MODELS: Live2DModel[] = [
  {
    id: 'default',
    name: '默认模型',
    description: '标准数字人模型',
    modelPath: '/models/default/model.model3.json',
    texturesPath: ['/models/default/textures/'],
    motionsPath: ['/models/default/motions/'],
    expressionsPath: ['/models/default/expressions/'],
    thumbnail: '/models/default/thumbnail.png'
  }
]

export const DEFAULT_EXPRESSIONS: Expression[] = [
  { id: 'default', name: '默认', file: 'default.exp3.json', weight: 1.0 },
  { id: 'happy', name: '开心', file: 'happy.exp3.json', weight: 1.0 },
  { id: 'sad', name: '难过', file: 'sad.exp3.json', weight: 1.0 },
  { id: 'surprised', name: '惊讶', file: 'surprised.exp3.json', weight: 1.0 },
  { id: 'angry', name: '生气', file: 'angry.exp3.json', weight: 1.0 }
]

export const DEFAULT_MOTIONS: Motion[] = [
  { id: 'idle', name: '待机', file: 'idle.motion3.json', loop: true, fadeInTime: 0.5, fadeOutTime: 0.5 },
  { id: 'wave', name: '挥手', file: 'wave.motion3.json', loop: false, fadeInTime: 0.3, fadeOutTime: 0.3 },
  { id: 'nod', name: '点头', file: 'nod.motion3.json', loop: false, fadeInTime: 0.2, fadeOutTime: 0.2 }
]
