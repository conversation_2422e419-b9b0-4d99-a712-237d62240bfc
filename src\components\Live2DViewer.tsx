'use client'

import { useEffect, useRef, useState, useCallback } from 'react'
import { Live2DManager, createLive2DManager } from '@/utils/live2d'
import { FaceData } from '@/types'

interface Live2DViewerProps {
  modelId: string
  isVideoEnabled: boolean
  faceData?: FaceData
  currentExpression?: string
  onModelLoaded?: (modelId: string) => void
  onModelError?: (error: Error) => void
}

export default function Live2DViewer({
  modelId,
  isVideoEnabled,
  faceData,
  currentExpression,
  onModelLoaded,
  onModelError
}: Live2DViewerProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const managerRef = useRef<Live2DManager | null>(null)
  const animationFrameRef = useRef<number | null>(null)

  const [isModelLoaded, setIsModelLoaded] = useState(false)
  const [modelStatus, setModelStatus] = useState('正在初始化...')
  const [currentExpressionState, setCurrentExpressionState] = useState('default')
  const [fps, setFps] = useState(60)
  const [lastFrameTime, setLastFrameTime] = useState(0)

  // 初始化Live2D管理器
  useEffect(() => {
    const initializeManager = async () => {
      try {
        setModelStatus('初始化Live2D引擎...')
        managerRef.current = createLive2DManager()

        if (canvasRef.current) {
          managerRef.current.setCanvas(canvasRef.current)
        }

        setModelStatus('Live2D引擎初始化完成')
      } catch (error) {
        console.error('Failed to initialize Live2D manager:', error)
        setModelStatus('初始化失败')
        onModelError?.(error as Error)
      }
    }

    initializeManager()

    return () => {
      if (managerRef.current) {
        managerRef.current.dispose()
      }
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [onModelError])

  // 加载模型
  useEffect(() => {
    const loadModel = async () => {
      if (!managerRef.current) return

      try {
        setIsModelLoaded(false)
        setModelStatus(`正在加载模型: ${modelId}...`)

        // 模拟模型数据
        const modelData = {
          id: modelId,
          name: getModelName(modelId),
          description: getModelDescription(modelId),
          modelPath: `/models/${modelId}/model.model3.json`,
          texturesPath: [`/models/${modelId}/textures/`],
          motionsPath: [`/models/${modelId}/motions/`],
          expressionsPath: [`/models/${modelId}/expressions/`],
          thumbnail: `/models/${modelId}/thumbnail.png`
        }

        await managerRef.current.loadModel(modelData)

        setIsModelLoaded(true)
        setModelStatus('模型加载完成')
        onModelLoaded?.(modelId)

        // 开始渲染循环
        startRenderLoop()

      } catch (error) {
        console.error('Failed to load Live2D model:', error)
        setModelStatus('模型加载失败')
        onModelError?.(error as Error)
      }
    }

    loadModel()
  }, [modelId, onModelLoaded, onModelError])

  // 渲染循环
  const startRenderLoop = useCallback(() => {
    const render = (currentTime: number) => {
      if (!managerRef.current || !isModelLoaded) return

      // 计算FPS
      if (lastFrameTime > 0) {
        const deltaTime = currentTime - lastFrameTime
        const currentFps = Math.round(1000 / deltaTime)
        setFps(currentFps)
      }
      setLastFrameTime(currentTime)

      // 更新模型
      const deltaTimeSeconds = (currentTime - lastFrameTime) / 1000
      managerRef.current.updateModel(deltaTimeSeconds)

      // 继续渲染循环
      animationFrameRef.current = requestAnimationFrame(render)
    }

    animationFrameRef.current = requestAnimationFrame(render)
  }, [isModelLoaded, lastFrameTime])

  // 处理面部数据更新
  useEffect(() => {
    if (faceData && managerRef.current && isModelLoaded) {
      managerRef.current.updateFromFaceData(faceData)
    }
  }, [faceData, isModelLoaded])

  // 处理表情变化
  useEffect(() => {
    if (currentExpression && managerRef.current && isModelLoaded) {
      managerRef.current.setExpression(currentExpression)
      setCurrentExpressionState(currentExpression)
    }
  }, [currentExpression, isModelLoaded])

  // 画布点击处理
  const handleCanvasClick = useCallback((event: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isModelLoaded || !managerRef.current) return

    const canvas = canvasRef.current
    if (!canvas) return

    const rect = canvas.getBoundingClientRect()
    const x = event.clientX - rect.left
    const y = event.clientY - rect.top

    // 模拟点击交互
    const expressions = ['happy', 'surprised', 'wink', 'default']
    const randomExpression = expressions[Math.floor(Math.random() * expressions.length)]

    managerRef.current.setExpression(randomExpression)
    setCurrentExpressionState(randomExpression)

    console.log(`Clicked at (${x}, ${y}), changing expression to: ${randomExpression}`)
  }, [isModelLoaded])

  // 获取模型名称
  const getModelName = (id: string): string => {
    const names: { [key: string]: string } = {
      'default': '默认模型',
      'anime-girl': '动漫少女',
      'business-man': '商务男士',
      'virtual-idol': '虚拟偶像'
    }
    return names[id] || '未知模型'
  }

  // 获取模型描述
  const getModelDescription = (id: string): string => {
    const descriptions: { [key: string]: string } = {
      'default': '标准数字人模型',
      'anime-girl': '可爱的动漫风格',
      'business-man': '专业商务形象',
      'virtual-idol': '时尚偶像风格'
    }
    return descriptions[id] || '暂无描述'
  }

  return (
    <div className="relative w-full h-full bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center">
      {/* Live2D 画布 */}
      <canvas
        ref={canvasRef}
        className="live2d-canvas cursor-pointer max-w-full max-h-full"
        width={800}
        height={600}
        onClick={handleCanvasClick}
        style={{ 
          display: isModelLoaded ? 'block' : 'none',
          background: 'transparent'
        }}
      />

      {/* 加载状态 */}
      {!isModelLoaded && (
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 border-4 border-blue-200 border-t-blue-500 rounded-full animate-spin"></div>
          <p className="text-gray-600 mb-2">{modelStatus}</p>
          <p className="text-sm text-gray-400">模型: {modelId}</p>
        </div>
      )}

      {/* 模型信息面板 */}
      {isModelLoaded && (
        <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-lg">
          <div className="flex items-center space-x-2 mb-2">
            <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-gray-700">数字人已就绪</span>
          </div>
          <div className="text-xs text-gray-500 space-y-1">
            <p>模型: {getModelName(modelId)}</p>
            <p>表情: {currentExpressionState}</p>
            <p>状态: {isVideoEnabled ? '实时同步' : '待机中'}</p>
            {faceData && (
              <p>面部: {faceData.expressions ? '已检测' : '未检测'}</p>
            )}
          </div>
        </div>
      )}

      {/* 交互提示 */}
      {isModelLoaded && !isVideoEnabled && (
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
          <div className="bg-black/70 text-white px-4 py-2 rounded-full text-sm backdrop-blur-sm">
            点击数字人进行互动 ✨
          </div>
        </div>
      )}

      {/* 视频同步提示 */}
      {isModelLoaded && isVideoEnabled && (
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
          <div className="bg-blue-600/80 text-white px-4 py-2 rounded-full text-sm backdrop-blur-sm flex items-center space-x-2">
            <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
            <span>实时表情同步中</span>
          </div>
        </div>
      )}

      {/* 表情快捷按钮 */}
      {isModelLoaded && !isVideoEnabled && (
        <div className="absolute bottom-4 right-4 flex space-x-2">
          {['😊', '😮', '😉', '😐'].map((emoji, index) => {
            const expressions = ['happy', 'surprised', 'wink', 'default']
            return (
              <button
                key={index}
                className={`w-10 h-10 rounded-full bg-white/90 backdrop-blur-sm shadow-lg hover:scale-110 transition-transform ${
                  currentExpressionState === expressions[index] ? 'ring-2 ring-blue-500' : ''
                }`}
                onClick={() => {
                  if (managerRef.current) {
                    managerRef.current.setExpression(expressions[index])
                    setCurrentExpressionState(expressions[index])
                  }
                }}
                title={`切换到${expressions[index]}表情`}
              >
                {emoji}
              </button>
            )
          })}
        </div>
      )}

      {/* 性能监控 */}
      {isModelLoaded && process.env.NODE_ENV === 'development' && (
        <div className="absolute top-4 right-4 bg-black/70 text-white p-2 rounded text-xs font-mono">
          <div>FPS: {fps}</div>
          <div>Model: {getModelName(modelId)}</div>
          <div>Expression: {currentExpressionState}</div>
          {faceData && (
            <>
              <div>Face: Detected</div>
              <div>Eyes: L{(faceData.eyeOpenness.left * 100).toFixed(0)}% R{(faceData.eyeOpenness.right * 100).toFixed(0)}%</div>
              <div>Mouth: {(faceData.mouthOpenness * 100).toFixed(0)}%</div>
            </>
          )}
        </div>
      )}
    </div>
  )
}
