// 实时交互管理器
import { FaceData } from '@/types'
import { 
  ExpressionSyncManager, 
  createExpressionSyncManager, 
  DEFAULT_EXPRESSION_SYNC_CONFIG 
} from './expression-sync-manager'
import { 
  LipSyncManager, 
  createLipSyncManager, 
  DEFAULT_LIP_SYNC_CONFIG 
} from './lip-sync-manager'

/**
 * 实时交互配置
 */
export interface RealtimeInteractionConfig {
  enableExpressionSync: boolean
  enableLipSync: boolean
  enableGestureRecognition: boolean
  enableEmotionalResponse: boolean
  interactionSensitivity: number
  responseDelay: number
}

/**
 * 交互事件
 */
export interface InteractionEvents {
  onExpressionChanged?: (expression: string, intensity: number) => void
  onParameterUpdated?: (parameterId: string, value: number) => void
  onMouthShapeChanged?: (shape: string, intensity: number) => void
  onGestureDetected?: (gesture: string, confidence: number) => void
  onEmotionChanged?: (emotion: string, intensity: number) => void
  onInteractionTriggered?: (type: string, data: any) => void
}

/**
 * 手势识别结果
 */
export interface GestureResult {
  gesture: string
  confidence: number
  timestamp: number
}

/**
 * 情感状态
 */
export interface EmotionalState {
  primary: string
  secondary: string
  intensity: number
  duration: number
}

/**
 * 实时交互管理器
 */
export class RealtimeInteractionManager {
  private config: RealtimeInteractionConfig
  private events: InteractionEvents
  private expressionSync: ExpressionSyncManager
  private lipSync: LipSyncManager
  private isActive: boolean = false
  private currentEmotion: EmotionalState
  private gestureHistory: GestureResult[] = []
  private interactionQueue: Array<{ type: string; data: any; timestamp: number }> = []

  constructor(config: RealtimeInteractionConfig, events: InteractionEvents = {}) {
    this.config = config
    this.events = events

    // 初始化表情同步管理器
    this.expressionSync = createExpressionSyncManager(
      DEFAULT_EXPRESSION_SYNC_CONFIG,
      {
        onExpressionChanged: (expression, weight) => {
          this.events.onExpressionChanged?.(expression, weight)
        },
        onParameterUpdated: (parameterId, value) => {
          this.events.onParameterUpdated?.(parameterId, value)
        },
        onEmotionDetected: (emotion, confidence) => {
          this.updateEmotionalState(emotion, confidence)
        }
      }
    )

    // 初始化嘴部动画管理器
    this.lipSync = createLipSyncManager(
      DEFAULT_LIP_SYNC_CONFIG,
      {
        onMouthShapeChanged: (shape, intensity) => {
          this.events.onMouthShapeChanged?.(shape, intensity)
        },
        onSpeechDetected: (isDetected) => {
          if (isDetected) {
            this.triggerInteraction('speech_detected', { timestamp: Date.now() })
          }
        }
      }
    )

    // 初始化情感状态
    this.currentEmotion = {
      primary: 'neutral',
      secondary: 'calm',
      intensity: 0.5,
      duration: 0
    }
  }

  /**
   * 初始化实时交互系统
   */
  async initialize(): Promise<void> {
    try {
      // 初始化嘴部动画系统
      if (this.config.enableLipSync) {
        await this.lipSync.initialize()
      }

      console.log('Realtime interaction manager initialized')
    } catch (error) {
      console.error('Failed to initialize realtime interaction manager:', error)
      throw error
    }
  }

  /**
   * 开始实时交互
   */
  start(): void {
    if (this.isActive) return

    this.isActive = true

    // 启动表情同步
    if (this.config.enableExpressionSync) {
      this.expressionSync.start()
    }

    // 启动嘴部动画
    if (this.config.enableLipSync) {
      this.lipSync.start()
    }

    // 开始处理交互队列
    this.startInteractionProcessing()

    console.log('Realtime interaction started')
  }

  /**
   * 停止实时交互
   */
  stop(): void {
    if (!this.isActive) return

    this.isActive = false

    // 停止表情同步
    this.expressionSync.stop()

    // 停止嘴部动画
    this.lipSync.stop()

    console.log('Realtime interaction stopped')
  }

  /**
   * 更新面部数据
   */
  updateFaceData(faceData: FaceData): void {
    if (!this.isActive) return

    // 更新表情同步
    if (this.config.enableExpressionSync) {
      this.expressionSync.updateFaceData(faceData)
    }

    // 手势识别
    if (this.config.enableGestureRecognition) {
      this.recognizeGestures(faceData)
    }
  }

  /**
   * 处理语音输入
   */
  processSpeech(text: string, audioData?: ArrayBuffer): void {
    if (!this.isActive) return

    // 触发嘴部动画
    if (this.config.enableLipSync && text) {
      this.lipSync.animateFromText(text)
    }

    // 分析情感内容
    if (this.config.enableEmotionalResponse) {
      this.analyzeTextEmotion(text)
    }

    // 触发交互事件
    this.triggerInteraction('speech_processed', { text, timestamp: Date.now() })
  }

  /**
   * 识别手势
   */
  private recognizeGestures(faceData: FaceData): void {
    try {
      const gestures = this.detectGesturesFromFace(faceData)
      
      for (const gesture of gestures) {
        this.addGestureToHistory(gesture)
        
        if (gesture.confidence > 0.7) {
          this.events.onGestureDetected?.(gesture.gesture, gesture.confidence)
          this.triggerInteraction('gesture_detected', gesture)
        }
      }
    } catch (error) {
      console.error('Gesture recognition error:', error)
    }
  }

  /**
   * 从面部数据检测手势
   */
  private detectGesturesFromFace(faceData: FaceData): GestureResult[] {
    const gestures: GestureResult[] = []
    const timestamp = Date.now()

    // 检测点头
    if (Math.abs(faceData.headRotation.x) > 15) {
      gestures.push({
        gesture: faceData.headRotation.x > 0 ? 'nod_down' : 'nod_up',
        confidence: Math.min(Math.abs(faceData.headRotation.x) / 30, 1),
        timestamp
      })
    }

    // 检测摇头
    if (Math.abs(faceData.headRotation.y) > 20) {
      gestures.push({
        gesture: faceData.headRotation.y > 0 ? 'shake_right' : 'shake_left',
        confidence: Math.min(Math.abs(faceData.headRotation.y) / 40, 1),
        timestamp
      })
    }

    // 检测眨眼
    if (faceData.eyeOpenness.left < 0.3 && faceData.eyeOpenness.right < 0.3) {
      gestures.push({
        gesture: 'blink',
        confidence: 1 - Math.max(faceData.eyeOpenness.left, faceData.eyeOpenness.right),
        timestamp
      })
    }

    // 检测单眼眨眼
    if (faceData.eyeOpenness.left < 0.3 && faceData.eyeOpenness.right > 0.7) {
      gestures.push({
        gesture: 'wink_left',
        confidence: 1 - faceData.eyeOpenness.left,
        timestamp
      })
    } else if (faceData.eyeOpenness.right < 0.3 && faceData.eyeOpenness.left > 0.7) {
      gestures.push({
        gesture: 'wink_right',
        confidence: 1 - faceData.eyeOpenness.right,
        timestamp
      })
    }

    return gestures
  }

  /**
   * 添加手势到历史记录
   */
  private addGestureToHistory(gesture: GestureResult): void {
    this.gestureHistory.push(gesture)

    // 保持历史记录大小
    if (this.gestureHistory.length > 50) {
      this.gestureHistory.shift()
    }
  }

  /**
   * 分析文本情感
   */
  private analyzeTextEmotion(text: string): void {
    // 简单的情感分析
    const emotionKeywords = {
      happy: ['开心', '高兴', '快乐', '哈哈', '笑', '好棒', '太好了'],
      sad: ['难过', '伤心', '哭', '痛苦', '失望'],
      excited: ['兴奋', '激动', '太棒了', 'amazing'],
      angry: ['生气', '愤怒', '讨厌', '烦'],
      surprised: ['惊讶', '震惊', '不敢相信', '天哪', '哇']
    }

    let detectedEmotion = 'neutral'
    let maxScore = 0

    for (const [emotion, keywords] of Object.entries(emotionKeywords)) {
      let score = 0
      keywords.forEach(keyword => {
        if (text.includes(keyword)) {
          score += 1
        }
      })

      if (score > maxScore) {
        maxScore = score
        detectedEmotion = emotion
      }
    }

    if (maxScore > 0) {
      const intensity = Math.min(maxScore / 3, 1)
      this.updateEmotionalState(detectedEmotion, intensity)
    }
  }

  /**
   * 更新情感状态
   */
  private updateEmotionalState(emotion: string, intensity: number): void {
    const previousEmotion = this.currentEmotion.primary

    this.currentEmotion = {
      primary: emotion,
      secondary: previousEmotion,
      intensity: intensity * this.config.interactionSensitivity,
      duration: Date.now()
    }

    this.events.onEmotionChanged?.(emotion, this.currentEmotion.intensity)

    // 触发情感响应
    if (this.config.enableEmotionalResponse) {
      this.triggerEmotionalResponse(emotion, intensity)
    }
  }

  /**
   * 触发情感响应
   */
  private triggerEmotionalResponse(emotion: string, intensity: number): void {
    // 根据情感调整表情和动作
    const responses = {
      happy: () => {
        this.expressionSync.setExpression('happy', intensity)
        this.triggerInteraction('emotional_response', { emotion, action: 'smile' })
      },
      sad: () => {
        this.expressionSync.setExpression('sad', intensity)
        this.triggerInteraction('emotional_response', { emotion, action: 'frown' })
      },
      surprised: () => {
        this.expressionSync.setExpression('surprised', intensity)
        this.triggerInteraction('emotional_response', { emotion, action: 'gasp' })
      },
      excited: () => {
        this.expressionSync.setExpression('happy', intensity)
        this.triggerInteraction('emotional_response', { emotion, action: 'cheer' })
      }
    }

    const response = responses[emotion as keyof typeof responses]
    if (response) {
      setTimeout(response, this.config.responseDelay)
    }
  }

  /**
   * 触发交互事件
   */
  private triggerInteraction(type: string, data: any): void {
    const interaction = {
      type,
      data,
      timestamp: Date.now()
    }

    this.interactionQueue.push(interaction)
    this.events.onInteractionTriggered?.(type, data)
  }

  /**
   * 开始处理交互队列
   */
  private startInteractionProcessing(): void {
    const processQueue = () => {
      if (!this.isActive) return

      // 处理队列中的交互事件
      while (this.interactionQueue.length > 0) {
        const interaction = this.interactionQueue.shift()
        if (interaction) {
          this.processInteraction(interaction)
        }
      }

      // 继续处理
      setTimeout(processQueue, 100)
    }

    processQueue()
  }

  /**
   * 处理单个交互事件
   */
  private processInteraction(interaction: { type: string; data: any; timestamp: number }): void {
    // 这里可以添加复杂的交互逻辑
    console.log('Processing interaction:', interaction.type, interaction.data)
  }

  /**
   * 获取当前状态
   */
  getStatus(): {
    isActive: boolean
    currentEmotion: EmotionalState
    recentGestures: GestureResult[]
    queueLength: number
  } {
    return {
      isActive: this.isActive,
      currentEmotion: this.currentEmotion,
      recentGestures: this.gestureHistory.slice(-5),
      queueLength: this.interactionQueue.length
    }
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<RealtimeInteractionConfig>): void {
    this.config = { ...this.config, ...newConfig }
  }

  /**
   * 释放资源
   */
  dispose(): void {
    this.stop()
    this.expressionSync.dispose()
    this.lipSync.dispose()
    this.gestureHistory = []
    this.interactionQueue = []
  }
}

/**
 * 默认配置
 */
export const DEFAULT_REALTIME_INTERACTION_CONFIG: RealtimeInteractionConfig = {
  enableExpressionSync: true,
  enableLipSync: true,
  enableGestureRecognition: true,
  enableEmotionalResponse: true,
  interactionSensitivity: 1.0,
  responseDelay: 300
}

/**
 * 创建实时交互管理器
 */
export const createRealtimeInteractionManager = (
  config: RealtimeInteractionConfig = DEFAULT_REALTIME_INTERACTION_CONFIG,
  events: InteractionEvents = {}
) => {
  return new RealtimeInteractionManager(config, events)
}
