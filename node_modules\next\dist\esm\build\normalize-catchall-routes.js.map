{"version": 3, "sources": ["../../src/build/normalize-catchall-routes.ts"], "names": ["AppPathnameNormalizer", "normalizeCatchAllRoutes", "appPaths", "normalizer", "catchAllRoutes", "Set", "Object", "values", "flat", "filter", "isCatchAllRoute", "sort", "a", "b", "split", "length", "appPath", "keys", "catchAllRoute", "normalizedCatchAllRoute", "normalize", "normalizedCatchAllRouteBasePath", "slice", "search", "catchAllRouteRegex", "startsWith", "some", "path", "hasMatchedSlots", "push", "path1", "path2", "slots1", "segment", "slots2", "i", "pathname", "includes"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,iEAAgE;AAEtG;;;;;;CAMC,GACD,OAAO,SAASC,wBACdC,QAAkC,EAClCC,aAAa,IAAIH,uBAAuB;IAExC,MAAMI,iBAAiB;WAClB,IAAIC,IACLC,OAAOC,MAAM,CAACL,UACXM,IAAI,GACJC,MAAM,CAACC,gBACR,wEAAwE;SACvEC,IAAI,CAAC,CAACC,GAAGC,IAAMA,EAAEC,KAAK,CAAC,KAAKC,MAAM,GAAGH,EAAEE,KAAK,CAAC,KAAKC,MAAM;KAE9D;IAED,KAAK,MAAMC,WAAWV,OAAOW,IAAI,CAACf,UAAW;QAC3C,KAAK,MAAMgB,iBAAiBd,eAAgB;YAC1C,MAAMe,0BAA0BhB,WAAWiB,SAAS,CAACF;YACrD,MAAMG,kCAAkCF,wBAAwBG,KAAK,CACnE,GACAH,wBAAwBI,MAAM,CAACC;YAGjC,IACE,uDAAuD;YACvDR,QAAQS,UAAU,CAACJ,oCACnB,gFAAgF;YAChF,CAACnB,QAAQ,CAACc,QAAQ,CAACU,IAAI,CAAC,CAACC,OAASC,gBAAgBD,MAAMT,iBACxD;gBACAhB,QAAQ,CAACc,QAAQ,CAACa,IAAI,CAACX;YACzB;QACF;IACF;AACF;AAEA,SAASU,gBAAgBE,KAAa,EAAEC,KAAa;IACnD,MAAMC,SAASF,MAAMhB,KAAK,CAAC,KAAKL,MAAM,CAAC,CAACwB,UAAYA,QAAQR,UAAU,CAAC;IACvE,MAAMS,SAASH,MAAMjB,KAAK,CAAC,KAAKL,MAAM,CAAC,CAACwB,UAAYA,QAAQR,UAAU,CAAC;IAEvE,IAAIO,OAAOjB,MAAM,KAAKmB,OAAOnB,MAAM,EAAE,OAAO;IAE5C,IAAK,IAAIoB,IAAI,GAAGA,IAAIH,OAAOjB,MAAM,EAAEoB,IAAK;QACtC,IAAIH,MAAM,CAACG,EAAE,KAAKD,MAAM,CAACC,EAAE,EAAE,OAAO;IACtC;IAEA,OAAO;AACT;AAEA,MAAMX,qBAAqB;AAE3B,SAASd,gBAAgB0B,QAAgB;IACvC,OAAOA,SAASC,QAAQ,CAAC,WAAWD,SAASC,QAAQ,CAAC;AACxD"}