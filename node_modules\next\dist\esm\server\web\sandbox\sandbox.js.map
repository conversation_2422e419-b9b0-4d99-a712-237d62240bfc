{"version": 3, "sources": ["../../../../src/server/web/sandbox/sandbox.ts"], "names": ["getModuleContext", "requestToBodyStream", "NEXT_RSC_UNION_QUERY", "ErrorSource", "Symbol", "FORBIDDEN_HEADERS", "withTaggedErrors", "fn", "process", "env", "NODE_ENV", "getServerError", "require", "params", "then", "result", "waitUntil", "catch", "error", "getRuntimeContext", "runtime", "evaluateInContext", "moduleName", "name", "onWarning", "useCache", "edgeFunctionEntry", "distDir", "incrementalCache", "context", "globalThis", "__incrementalCache", "<PERSON><PERSON><PERSON><PERSON>", "paths", "run", "runWithTaggedErrors", "subreq", "request", "headers", "subrequests", "split", "includes", "Promise", "resolve", "response", "Response", "edgeFunction", "_ENTRIES", "default", "cloned", "method", "body", "cloneBodyStream", "undefined", "KUint8Array", "evaluate", "urlInstance", "URL", "url", "searchParams", "delete", "toString", "headerName", "finalize"], "mappings": "AAGA,SAASA,gBAAgB,QAAQ,YAAW;AAC5C,SAASC,mBAAmB,QAAQ,qBAAoB;AACxD,SAASC,oBAAoB,QAAQ,gDAA+C;AAEpF,OAAO,MAAMC,cAAcC,OAAO,gBAAe;AAEjD,MAAMC,oBAAoB;IACxB;IACA;IACA;CACD;AAaD;;;CAGC,GACD,SAASC,iBAAiBC,EAAY;IACpC,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,MAAM,EAAEC,cAAc,EAAE,GACtBC,QAAQ;QAEV,OAAO,CAACC,SACNN,GAAGM,QACAC,IAAI,CAAC,CAACC;oBAEMA;uBAFM;oBACjB,GAAGA,MAAM;oBACTC,SAAS,EAAED,2BAAAA,oBAAAA,OAAQC,SAAS,qBAAjBD,kBAAmBE,KAAK,CAAC,CAACC;wBACnC,mGAAmG;wBACnG,MAAMP,eAAeO,OAAO;oBAC9B;gBACF;eACCD,KAAK,CAAC,CAACC;gBACN,+CAA+C;gBAC/C,MAAMP,eAAeO,OAAO;YAC9B;IACN;IAEA,OAAOX;AACT;AAEA,OAAO,eAAeY,kBAAkBN,MAQvC;IACC,MAAM,EAAEO,OAAO,EAAEC,iBAAiB,EAAE,GAAG,MAAMrB,iBAAiB;QAC5DsB,YAAYT,OAAOU,IAAI;QACvBC,WAAWX,OAAOW,SAAS,IAAK,CAAA,KAAO,CAAA;QACvCC,UAAUZ,OAAOY,QAAQ,KAAK;QAC9BC,mBAAmBb,OAAOa,iBAAiB;QAC3CC,SAASd,OAAOc,OAAO;IACzB;IAEA,IAAId,OAAOe,gBAAgB,EAAE;QAC3BR,QAAQS,OAAO,CAACC,UAAU,CAACC,kBAAkB,GAAGlB,OAAOe,gBAAgB;IACzE;IAEA,KAAK,MAAMI,aAAanB,OAAOoB,KAAK,CAAE;QACpCZ,kBAAkBW;IACpB;IACA,OAAOZ;AACT;AAEA,OAAO,MAAMc,MAAM5B,iBAAiB,eAAe6B,oBAAoBtB,MAAM;QAqBvEA;IApBJ,MAAMO,UAAU,MAAMD,kBAAkBN;IACxC,MAAMuB,SAASvB,OAAOwB,OAAO,CAACC,OAAO,CAAC,CAAC,uBAAuB,CAAC,CAAC;IAChE,MAAMC,cAAc,OAAOH,WAAW,WAAWA,OAAOI,KAAK,CAAC,OAAO,EAAE;IACvE,IAAID,YAAYE,QAAQ,CAAC5B,OAAOU,IAAI,GAAG;QACrC,OAAO;YACLP,WAAW0B,QAAQC,OAAO;YAC1BC,UAAU,IAAIxB,QAAQS,OAAO,CAACgB,QAAQ,CAAC,MAAM;gBAC3CP,SAAS;oBACP,qBAAqB;gBACvB;YACF;QACF;IACF;IAEA,MAAMQ,eAGJ1B,QAAQS,OAAO,CAACkB,QAAQ,CAAC,CAAC,WAAW,EAAElC,OAAOU,IAAI,CAAC,CAAC,CAAC,CAACyB,OAAO;IAE/D,MAAMC,SAAS,CAAC;QAAC;QAAQ;KAAM,CAACR,QAAQ,CAAC5B,OAAOwB,OAAO,CAACa,MAAM,KAC1DrC,uBAAAA,OAAOwB,OAAO,CAACc,IAAI,qBAAnBtC,qBAAqBuC,eAAe,KACpCC;IAEJ,MAAMC,cAAclC,QAAQmC,QAAQ,CAAC;IACrC,MAAMC,cAAc,IAAIC,IAAI5C,OAAOwB,OAAO,CAACqB,GAAG;IAC9CF,YAAYG,YAAY,CAACC,MAAM,CAAC1D;IAEhCW,OAAOwB,OAAO,CAACqB,GAAG,GAAGF,YAAYK,QAAQ;IAEzC,IAAI;QACF,MAAM9C,SAAS,MAAM+B,aAAa;YAChCT,SAAS;gBACP,GAAGxB,OAAOwB,OAAO;gBACjBc,MACEF,UAAUhD,oBAAoBmB,QAAQS,OAAO,EAAEyB,aAAaL;YAChE;QACF;QACA,KAAK,MAAMa,cAAczD,kBAAmB;YAC1CU,OAAO6B,QAAQ,CAACN,OAAO,CAACsB,MAAM,CAACE;QACjC;QACA,OAAO/C;IACT,SAAU;YACFF;QAAN,QAAMA,wBAAAA,OAAOwB,OAAO,CAACc,IAAI,qBAAnBtC,sBAAqBkD,QAAQ;IACrC;AACF,GAAE"}