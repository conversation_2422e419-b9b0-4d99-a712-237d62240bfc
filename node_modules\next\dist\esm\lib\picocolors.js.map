{"version": 3, "sources": ["../../src/lib/picocolors.ts"], "names": ["globalThis", "env", "stdout", "process", "enabled", "NO_COLOR", "FORCE_COLOR", "isTTY", "CI", "TERM", "replaceClose", "str", "close", "replace", "index", "start", "substring", "end", "length", "nextIndex", "indexOf", "formatter", "open", "input", "string", "reset", "s", "String", "bold", "dim", "italic", "underline", "inverse", "hidden", "strikethrough", "black", "red", "green", "yellow", "blue", "magenta", "purple", "cyan", "white", "gray", "bgBlack", "bgRed", "bgGreen", "bgYellow", "bgBlue", "bgMagenta", "bg<PERSON>yan", "bgWhite"], "mappings": "AAAA,cAAc;AAEd,wEAAwE;AAExE,2EAA2E;AAC3E,yEAAyE;AACzE,oEAAoE;AAEpE,2EAA2E;AAC3E,mEAAmE;AACnE,0EAA0E;AAC1E,yEAAyE;AACzE,wEAAwE;AACxE,0EAA0E;AAC1E,iEAAiE;AACjE,EAAE;AACF,8GAA8G;IAEtFA;AAAxB,MAAM,EAAEC,GAAG,EAAEC,MAAM,EAAE,GAAGF,EAAAA,cAAAA,+BAAAA,YAAYG,OAAO,KAAI,CAAC;AAEhD,MAAMC,UACJH,OACA,CAACA,IAAII,QAAQ,IACZJ,CAAAA,IAAIK,WAAW,IAAKJ,CAAAA,0BAAAA,OAAQK,KAAK,KAAI,CAACN,IAAIO,EAAE,IAAIP,IAAIQ,IAAI,KAAK,MAAM;AAEtE,MAAMC,eAAe,CACnBC,KACAC,OACAC,SACAC;IAEA,MAAMC,QAAQJ,IAAIK,SAAS,CAAC,GAAGF,SAASD;IACxC,MAAMI,MAAMN,IAAIK,SAAS,CAACF,QAAQF,MAAMM,MAAM;IAC9C,MAAMC,YAAYF,IAAIG,OAAO,CAACR;IAC9B,OAAO,CAACO,YACJJ,QAAQL,aAAaO,KAAKL,OAAOC,SAASM,aAC1CJ,QAAQE;AACd;AAEA,MAAMI,YACJ,CAACC,MAAcV,OAAeC,UAAUS,IAAI,GAC5C,CAACC;QACC,MAAMC,SAAS,KAAKD;QACpB,MAAMT,QAAQU,OAAOJ,OAAO,CAACR,OAAOU,KAAKJ,MAAM;QAC/C,OAAO,CAACJ,QACJQ,OAAOZ,aAAac,QAAQZ,OAAOC,SAASC,SAASF,QACrDU,OAAOE,SAASZ;IACtB;AAEF,OAAO,MAAMa,QAAQrB,UAAU,CAACsB,IAAc,CAAC,OAAO,EAAEA,EAAE,OAAO,CAAC,GAAGC,OAAM;AAC3E,OAAO,MAAMC,OAAOxB,UAChBiB,UAAU,WAAW,YAAY,qBACjCM,OAAM;AACV,OAAO,MAAME,MAAMzB,UACfiB,UAAU,WAAW,YAAY,qBACjCM,OAAM;AACV,OAAO,MAAMG,SAAS1B,UAAUiB,UAAU,WAAW,cAAcM,OAAM;AACzE,OAAO,MAAMI,YAAY3B,UAAUiB,UAAU,WAAW,cAAcM,OAAM;AAC5E,OAAO,MAAMK,UAAU5B,UAAUiB,UAAU,WAAW,cAAcM,OAAM;AAC1E,OAAO,MAAMM,SAAS7B,UAAUiB,UAAU,WAAW,cAAcM,OAAM;AACzE,OAAO,MAAMO,gBAAgB9B,UAAUiB,UAAU,WAAW,cAAcM,OAAM;AAChF,OAAO,MAAMQ,QAAQ/B,UAAUiB,UAAU,YAAY,cAAcM,OAAM;AACzE,OAAO,MAAMS,MAAMhC,UAAUiB,UAAU,YAAY,cAAcM,OAAM;AACvE,OAAO,MAAMU,QAAQjC,UAAUiB,UAAU,YAAY,cAAcM,OAAM;AACzE,OAAO,MAAMW,SAASlC,UAAUiB,UAAU,YAAY,cAAcM,OAAM;AAC1E,OAAO,MAAMY,OAAOnC,UAAUiB,UAAU,YAAY,cAAcM,OAAM;AACxE,OAAO,MAAMa,UAAUpC,UAAUiB,UAAU,YAAY,cAAcM,OAAM;AAC3E,OAAO,MAAMc,SAASrC,UAClBiB,UAAU,0BAA0B,cACpCM,OAAM;AACV,OAAO,MAAMe,OAAOtC,UAAUiB,UAAU,YAAY,cAAcM,OAAM;AACxE,OAAO,MAAMgB,QAAQvC,UAAUiB,UAAU,YAAY,cAAcM,OAAM;AACzE,OAAO,MAAMiB,OAAOxC,UAAUiB,UAAU,YAAY,cAAcM,OAAM;AACxE,OAAO,MAAMkB,UAAUzC,UAAUiB,UAAU,YAAY,cAAcM,OAAM;AAC3E,OAAO,MAAMmB,QAAQ1C,UAAUiB,UAAU,YAAY,cAAcM,OAAM;AACzE,OAAO,MAAMoB,UAAU3C,UAAUiB,UAAU,YAAY,cAAcM,OAAM;AAC3E,OAAO,MAAMqB,WAAW5C,UAAUiB,UAAU,YAAY,cAAcM,OAAM;AAC5E,OAAO,MAAMsB,SAAS7C,UAAUiB,UAAU,YAAY,cAAcM,OAAM;AAC1E,OAAO,MAAMuB,YAAY9C,UAAUiB,UAAU,YAAY,cAAcM,OAAM;AAC7E,OAAO,MAAMwB,SAAS/C,UAAUiB,UAAU,YAAY,cAAcM,OAAM;AAC1E,OAAO,MAAMyB,UAAUhD,UAAUiB,UAAU,YAAY,cAAcM,OAAM"}