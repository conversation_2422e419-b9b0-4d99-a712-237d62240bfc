{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/restore-reducer.ts"], "names": ["createHrefFromUrl", "extractPathFromFlightRouterState", "restoreReducer", "state", "action", "url", "tree", "href", "buildId", "canonicalUrl", "pushRef", "pendingPush", "mpaNavigation", "preserveCustomHistoryState", "focusAndScrollRef", "cache", "prefetchCache", "nextUrl", "pathname"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,0BAAyB;AAM3D,SAASC,gCAAgC,QAAQ,0BAAyB;AAE1E,OAAO,SAASC,eACdC,KAA2B,EAC3BC,MAAqB;IAErB,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE,GAAGF;IACtB,MAAMG,OAAOP,kBAAkBK;QAiBpBJ;IAfX,OAAO;QACLO,SAASL,MAAMK,OAAO;QACtB,oBAAoB;QACpBC,cAAcF;QACdG,SAAS;YACPC,aAAa;YACbC,eAAe;YACf,6FAA6F;YAC7FC,4BAA4B;QAC9B;QACAC,mBAAmBX,MAAMW,iBAAiB;QAC1CC,OAAOZ,MAAMY,KAAK;QAClBC,eAAeb,MAAMa,aAAa;QAClC,wBAAwB;QACxBV,MAAMA;QACNW,SAAShB,CAAAA,oCAAAA,iCAAiCK,iBAAjCL,oCAA0CI,IAAIa,QAAQ;IACjE;AACF"}