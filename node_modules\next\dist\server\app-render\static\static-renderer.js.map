{"version": 3, "sources": ["../../../../src/server/app-render/static/static-renderer.ts"], "names": ["<PERSON><PERSON><PERSON><PERSON>", "createStatic<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "options", "prerender", "process", "env", "__NEXT_EXPERIMENTAL_REACT", "require", "render", "children", "prelude", "postponed", "stream", "StaticResumeRenderer", "resume", "renderToReadableStream", "ppr", "isStaticGeneration", "streamOptions", "onError", "onHeaders", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nonce", "bootstrapScripts", "formState"], "mappings": ";;;;;;;;;;;;;;;IA8CaA,cAAc;eAAdA;;IAyDGC,oBAAoB;eAApBA;;;AAxFhB,MAAMC;IAMJC,YAA6BC,QAA2B;uBAA3BA;aAJZC,YAAaC,QAAQC,GAAG,CAACC,yBAAyB,GAC/DC,QAAQ,yBAAyBJ,SAAS,GAC1C;IAEqD;IAEzD,MAAaK,OAAOC,QAAqB,EAAE;QACzC,MAAM,EAAEC,OAAO,EAAEC,SAAS,EAAE,GAAG,MAAM,IAAI,CAACR,SAAS,CAACM,UAAU,IAAI,CAACP,OAAO;QAE1E,OAAO;YAAEU,QAAQF;YAASC;QAAU;IACtC;AACF;AAEA,MAAME;IAIJZ,YACmBU,WACAT,QACjB;yBAFiBS;uBACAT;aALFY,SAASP,QAAQ,yBAC/BO,MAAM;IAKN;IAEH,MAAaN,OAAOC,QAAqB,EAAE;QACzC,MAAMG,SAAS,MAAM,IAAI,CAACE,MAAM,CAACL,UAAU,IAAI,CAACE,SAAS,EAAE,IAAI,CAACT,OAAO;QAEvE,OAAO;YAAEU;QAAO;IAClB;AACF;AAEO,MAAMd;IAIXG,YAA6BC,QAAwC;uBAAxCA;aAHZa,yBAAyBR,QAAQ,yBAC/CQ,sBAAsB;IAE6C;IAEtE,MAAaP,OAAOC,QAAqB,EAAyB;QAChE,MAAMG,SAAS,MAAM,IAAI,CAACG,sBAAsB,CAACN,UAAU,IAAI,CAACP,OAAO;QACvE,OAAO;YAAEU;QAAO;IAClB;AACF;AA+CO,SAASb,qBAAqB,EACnCiB,GAAG,EACHC,kBAAkB,EAClBN,SAAS,EACTO,eAAe,EACbC,OAAO,EACPC,SAAS,EACTC,gBAAgB,EAChBC,KAAK,EACLC,gBAAgB,EAChBC,SAAS,EACV,EACO;IACR,IAAIR,KAAK;QACP,IAAIC,oBAAoB;YACtB,OAAO,IAAIjB,eAAe;gBACxBmB;gBACAC;gBACAC;gBACAE;YACF;QACF;QAEA,IAAIZ,WAAW;YACb,OAAO,IAAIE,qBAAqBF,WAAW;gBACzCQ;gBACAG;YACF;QACF;IACF;IAEA,OAAO,IAAIxB,eAAe;QACxBqB;QACAC;QACAC;QACAC;QACAC;QACAC;IACF;AACF"}