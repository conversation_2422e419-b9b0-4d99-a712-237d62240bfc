{"version": 3, "sources": ["../../../src/server/lib/server-action-request-meta.ts"], "names": ["getServerActionRequestMetadata", "getIsServerAction", "req", "actionId", "contentType", "headers", "Headers", "get", "ACTION", "toLowerCase", "isURLEncodedAction", "Boolean", "method", "isMultipartAction", "startsWith", "isFetchAction", "undefined"], "mappings": ";;;;;;;;;;;;;;;IAKgBA,8BAA8B;eAA9BA;;IAkCAC,iBAAiB;eAAjBA;;;kCApCO;AAEhB,SAASD,+BACdE,GAAoD;IAOpD,IAAIC;IACJ,IAAIC;IAEJ,IAAIF,IAAIG,OAAO,YAAYC,SAAS;QAClCH,WAAWD,IAAIG,OAAO,CAACE,GAAG,CAACC,wBAAM,CAACC,WAAW,OAAO;QACpDL,cAAcF,IAAIG,OAAO,CAACE,GAAG,CAAC;IAChC,OAAO;QACLJ,WAAW,AAACD,IAAIG,OAAO,CAACG,wBAAM,CAACC,WAAW,GAAG,IAAe;QAC5DL,cAAcF,IAAIG,OAAO,CAAC,eAAe,IAAI;IAC/C;IAEA,MAAMK,qBAAqBC,QACzBT,IAAIU,MAAM,KAAK,UAAUR,gBAAgB;IAE3C,MAAMS,oBAAoBF,QACxBT,IAAIU,MAAM,KAAK,WAAUR,+BAAAA,YAAaU,UAAU,CAAC;IAEnD,MAAMC,gBAAgBJ,QACpBR,aAAaa,aACX,OAAOb,aAAa,YACpBD,IAAIU,MAAM,KAAK;IAGnB,OAAO;QAAET;QAAUO;QAAoBG;QAAmBE;IAAc;AAC1E;AAEO,SAASd,kBACdC,GAAoD;IAEpD,MAAM,EAAEa,aAAa,EAAEL,kBAAkB,EAAEG,iBAAiB,EAAE,GAC5Db,+BAA+BE;IAEjC,OAAOS,QAAQI,iBAAiBL,sBAAsBG;AACxD"}