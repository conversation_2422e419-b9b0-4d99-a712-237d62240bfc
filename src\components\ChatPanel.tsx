'use client'

import { useState, useRef, useEffect } from 'react'
import { Message } from '@/types'
import { AIChatManager, createAIChatManager, DEFAULT_AI_CHAT_CONFIG } from '@/lib/ai-chat-manager'

interface ChatPanelProps {
  messages: Message[]
  onSendMessage: (message: string) => void
  onMessageReceived?: (message: Message) => void
}

export default function ChatPanel({ messages, onSendMessage, onMessageReceived }: ChatPanelProps) {
  const [inputMessage, setInputMessage] = useState('')
  const [isRecording, setIsRecording] = useState(false)
  const [isTyping, setIsTyping] = useState(false)
  const [isSpeaking, setIsSpeaking] = useState(false)
  const [aiChatManager, setAiChatManager] = useState<AIChatManager | null>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // 初始化AI聊天管理器
  useEffect(() => {
    const initializeAIChat = async () => {
      try {
        const manager = createAIChatManager(DEFAULT_AI_CHAT_CONFIG, {
          onMessageReceived: (message) => {
            onMessageReceived?.(message)
          },
          onSpeechStart: () => setIsSpeaking(true),
          onSpeechEnd: () => setIsSpeaking(false),
          onListeningStart: () => setIsRecording(true),
          onListeningEnd: () => setIsRecording(false),
          onError: (error) => {
            console.error('AI Chat error:', error)
          }
        })

        await manager.initialize()
        setAiChatManager(manager)
      } catch (error) {
        console.error('Failed to initialize AI chat:', error)
      }
    }

    initializeAIChat()

    return () => {
      if (aiChatManager) {
        aiChatManager.dispose()
      }
    }
  }, [])

  // 自动滚动到最新消息
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  // 模拟AI正在输入状态
  useEffect(() => {
    if (messages.length > 0 && messages[messages.length - 1].type === 'user') {
      setIsTyping(true)
      const timer = setTimeout(() => setIsTyping(false), 1000)
      return () => clearTimeout(timer)
    }
  }, [messages])

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const handleVoiceRecord = async () => {
    if (!aiChatManager) return

    if (!isRecording) {
      try {
        console.log('开始语音识别...')
        const result = await aiChatManager.startListening()

        if (result.trim()) {
          setInputMessage(result)
          // 自动发送语音识别的结果
          await handleSendMessage(result)
        }
      } catch (error) {
        console.error('语音识别失败:', error)
        setIsRecording(false)
      }
    } else {
      aiChatManager.stopListening()
    }
  }

  const handleSendMessage = async (messageText?: string) => {
    const textToSend = messageText || inputMessage.trim()
    if (!textToSend || !aiChatManager) return

    try {
      setInputMessage('')
      inputRef.current?.focus()

      // 使用AI聊天管理器发送消息
      await aiChatManager.sendMessage(textToSend)
    } catch (error) {
      console.error('发送消息失败:', error)
      // 降级到原始方法
      onSendMessage(textToSend)
    }
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    })
  }

  return (
    <div className="bg-white rounded-xl shadow-lg h-96 flex flex-col">
      {/* 聊天头部 */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
              <span className="text-white font-bold">AI</span>
            </div>
            <div>
              <h3 className="font-semibold text-gray-800">数字人助手</h3>
              <p className="text-xs flex items-center">
                <div className={`w-2 h-2 rounded-full mr-1 ${
                  isSpeaking ? 'bg-blue-500 animate-pulse' : 'bg-green-500 animate-pulse'
                }`}></div>
                <span className={isSpeaking ? 'text-blue-500' : 'text-green-500'}>
                  {isSpeaking ? '正在说话' : '在线'}
                </span>
              </p>
            </div>
          </div>
          
          <div className="flex space-x-2">
            <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* 消息列表 */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length === 0 && (
          <div className="text-center text-gray-500 py-8">
            <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            </div>
            <p>开始与数字人对话吧！</p>
            <p className="text-sm mt-1">你可以输入文字或使用语音输入</p>
          </div>
        )}

        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'} chat-bubble`}
          >
            <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-2xl ${
              message.type === 'user'
                ? 'bg-blue-500 text-white'
                : 'bg-gray-100 text-gray-800'
            }`}>
              <p className="text-sm">{message.content}</p>
              <p className={`text-xs mt-1 ${
                message.type === 'user' ? 'text-blue-100' : 'text-gray-500'
              }`}>
                {formatTime(message.timestamp)}
              </p>
            </div>
          </div>
        ))}

        {/* AI正在输入指示器 */}
        {isTyping && (
          <div className="flex justify-start">
            <div className="bg-gray-100 px-4 py-2 rounded-2xl">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* 输入区域 */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex items-center space-x-2">
          <div className="flex-1 relative">
            <input
              ref={inputRef}
              type="text"
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="输入消息..."
              className="w-full px-4 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              disabled={isRecording}
            />
            {isRecording && (
              <div className="absolute inset-0 bg-red-50 border border-red-200 rounded-full flex items-center justify-center">
                <span className="text-red-600 text-sm">
                  🎤 {isRecording ? '正在识别...' : '正在录音...'}
                </span>
              </div>
            )}
          </div>

          {/* 语音输入按钮 */}
          <button
            onClick={handleVoiceRecord}
            className={`p-2 rounded-full transition-colors ${
              isRecording 
                ? 'bg-red-500 text-white recording-pulse' 
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
            title={isRecording ? '停止录音' : '语音输入'}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
            </svg>
          </button>

          {/* 发送按钮 */}
          <button
            onClick={handleSendMessage}
            disabled={!inputMessage.trim() || isRecording}
            className="p-2 bg-blue-500 text-white rounded-full hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
            title="发送消息"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
            </svg>
          </button>
        </div>

        {/* 快捷回复 */}
        <div className="flex flex-wrap gap-2 mt-3">
          {['你好', '介绍一下自己', '今天天气怎么样？', '唱首歌吧'].map((quickReply) => (
            <button
              key={quickReply}
              onClick={() => onSendMessage(quickReply)}
              className="px-3 py-1 text-xs bg-gray-100 text-gray-600 rounded-full hover:bg-gray-200 transition-colors"
            >
              {quickReply}
            </button>
          ))}
        </div>
      </div>
    </div>
  )
}
