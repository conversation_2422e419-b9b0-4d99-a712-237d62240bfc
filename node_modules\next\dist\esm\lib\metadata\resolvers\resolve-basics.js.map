{"version": 3, "sources": ["../../../../src/lib/metadata/resolvers/resolve-basics.ts"], "names": ["resolveAsArrayOrUndefined", "resolveAbsoluteUrlWithPathname", "resolveAlternateUrl", "url", "metadataBase", "pathname", "URL", "resolveThemeColor", "themeColor", "themeColorDescriptors", "for<PERSON>ach", "descriptor", "push", "color", "media", "resolveUrlValuesOfObject", "obj", "result", "key", "value", "Object", "entries", "item", "index", "title", "resolveCanonicalUrl", "urlOrDescriptor", "resolveAlternates", "alternates", "canonical", "languages", "types", "robotsKeys", "resolveRobotsValue", "robots", "values", "follow", "join", "resolveRobots", "basic", "googleBot", "VerificationKeys", "resolveVerification", "verification", "res", "other", "otherKey", "otherValue", "resolveAppleWebApp", "appWebApp", "capable", "startupImages", "startupImage", "map", "statusBarStyle", "resolveAppLinks", "appLinks", "resolveItunes", "itunes", "appId", "appArgument", "undefined"], "mappings": "AAWA,SAASA,yBAAyB,QAAQ,oBAAmB;AAC7D,SAASC,8BAA8B,QAAQ,gBAAe;AAE9D,SAASC,oBACPC,GAAiB,EACjBC,YAAwB,EACxBC,QAAgB;IAEhB,0CAA0C;IAC1C,8DAA8D;IAC9D,IAAIF,eAAeG,KAAK;QACtBH,MAAM,IAAIG,IAAID,UAAUF;IAC1B;IACA,OAAOF,+BAA+BE,KAAKC,cAAcC;AAC3D;AAEA,OAAO,MAAME,oBAAiD,CAACC;QAI7DR;IAHA,IAAI,CAACQ,YAAY,OAAO;IACxB,MAAMC,wBAAwD,EAAE;KAEhET,6BAAAA,0BAA0BQ,gCAA1BR,2BAAuCU,OAAO,CAAC,CAACC;QAC9C,IAAI,OAAOA,eAAe,UACxBF,sBAAsBG,IAAI,CAAC;YAAEC,OAAOF;QAAW;aAC5C,IAAI,OAAOA,eAAe,UAC7BF,sBAAsBG,IAAI,CAAC;YACzBC,OAAOF,WAAWE,KAAK;YACvBC,OAAOH,WAAWG,KAAK;QACzB;IACJ;IAEA,OAAOL;AACT,EAAC;AAED,SAASM,yBACPC,GAMa,EACbZ,YAA8C,EAC9CC,QAAgB;IAEhB,IAAI,CAACW,KAAK,OAAO;IAEjB,MAAMC,SAAoD,CAAC;IAC3D,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAACL,KAAM;QAC9C,IAAI,OAAOG,UAAU,YAAYA,iBAAiBb,KAAK;YACrDW,MAAM,CAACC,IAAI,GAAG;gBACZ;oBACEf,KAAKD,oBAAoBiB,OAAOf,cAAcC;gBAChD;aACD;QACH,OAAO;YACLY,MAAM,CAACC,IAAI,GAAG,EAAE;YAChBC,yBAAAA,MAAOT,OAAO,CAAC,CAACY,MAAMC;gBACpB,MAAMpB,MAAMD,oBAAoBoB,KAAKnB,GAAG,EAAEC,cAAcC;gBACxDY,MAAM,CAACC,IAAI,CAACK,MAAM,GAAG;oBACnBpB;oBACAqB,OAAOF,KAAKE,KAAK;gBACnB;YACF;QACF;IACF;IACA,OAAOP;AACT;AAEA,SAASQ,oBACPC,eAA0E,EAC1EtB,YAAwB,EACxBC,QAAgB;IAEhB,IAAI,CAACqB,iBAAiB,OAAO;IAE7B,MAAMvB,MACJ,OAAOuB,oBAAoB,YAAYA,2BAA2BpB,MAC9DoB,kBACAA,gBAAgBvB,GAAG;IAEzB,qEAAqE;IACrE,OAAO;QACLA,KAAKD,oBAAoBC,KAAKC,cAAcC;IAC9C;AACF;AAEA,OAAO,MAAMsB,oBAGT,CAACC,YAAYxB,cAAc,EAAEC,QAAQ,EAAE;IACzC,IAAI,CAACuB,YAAY,OAAO;IAExB,MAAMC,YAAYJ,oBAChBG,WAAWC,SAAS,EACpBzB,cACAC;IAEF,MAAMyB,YAAYf,yBAChBa,WAAWE,SAAS,EACpB1B,cACAC;IAEF,MAAMS,QAAQC,yBACZa,WAAWd,KAAK,EAChBV,cACAC;IAEF,MAAM0B,QAAQhB,yBACZa,WAAWG,KAAK,EAChB3B,cACAC;IAGF,MAAMY,SAAgC;QACpCY;QACAC;QACAhB;QACAiB;IACF;IAEA,OAAOd;AACT,EAAC;AAED,MAAMe,aAAa;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AACD,MAAMC,qBAAoE,CACxEC;IAEA,IAAI,CAACA,QAAQ,OAAO;IACpB,IAAI,OAAOA,WAAW,UAAU,OAAOA;IAEvC,MAAMC,SAAmB,EAAE;IAE3B,IAAID,OAAOX,KAAK,EAAEY,OAAOvB,IAAI,CAAC;SACzB,IAAI,OAAOsB,OAAOX,KAAK,KAAK,WAAWY,OAAOvB,IAAI,CAAC;IAExD,IAAIsB,OAAOE,MAAM,EAAED,OAAOvB,IAAI,CAAC;SAC1B,IAAI,OAAOsB,OAAOE,MAAM,KAAK,WAAWD,OAAOvB,IAAI,CAAC;IAEzD,KAAK,MAAMM,OAAOc,WAAY;QAC5B,MAAMb,QAAQe,MAAM,CAAChB,IAAI;QACzB,IAAI,OAAOC,UAAU,eAAeA,UAAU,OAAO;YACnDgB,OAAOvB,IAAI,CAAC,OAAOO,UAAU,YAAYD,MAAM,CAAC,EAAEA,IAAI,CAAC,EAAEC,MAAM,CAAC;QAClE;IACF;IAEA,OAAOgB,OAAOE,IAAI,CAAC;AACrB;AAEA,OAAO,MAAMC,gBAAyC,CAACJ;IACrD,IAAI,CAACA,QAAQ,OAAO;IACpB,OAAO;QACLK,OAAON,mBAAmBC;QAC1BM,WACE,OAAON,WAAW,WAAWD,mBAAmBC,OAAOM,SAAS,IAAI;IACxE;AACF,EAAC;AAED,MAAMC,mBAAmB;IAAC;IAAU;IAAS;IAAU;IAAM;CAAQ;AACrE,OAAO,MAAMC,sBAAqD,CAChEC;IAEA,IAAI,CAACA,cAAc,OAAO;IAC1B,MAAMC,MAA4B,CAAC;IAEnC,KAAK,MAAM1B,OAAOuB,iBAAkB;QAClC,MAAMtB,QAAQwB,YAAY,CAACzB,IAAI;QAC/B,IAAIC,OAAO;YACT,IAAID,QAAQ,SAAS;gBACnB0B,IAAIC,KAAK,GAAG,CAAC;gBACb,IAAK,MAAMC,YAAYH,aAAaE,KAAK,CAAE;oBACzC,MAAME,aAAa/C,0BACjB2C,aAAaE,KAAK,CAACC,SAAS;oBAE9B,IAAIC,YAAYH,IAAIC,KAAK,CAACC,SAAS,GAAGC;gBACxC;YACF,OAAOH,GAAG,CAAC1B,IAAI,GAAGlB,0BAA0BmB;QAC9C;IACF;IACA,OAAOyB;AACT,EAAC;AAED,OAAO,MAAMI,qBAAmD,CAACC;QAS3DjD;IARJ,IAAI,CAACiD,WAAW,OAAO;IACvB,IAAIA,cAAc,MAAM;QACtB,OAAO;YACLC,SAAS;QACX;IACF;IAEA,MAAMC,gBAAgBF,UAAUG,YAAY,IACxCpD,6BAAAA,0BAA0BiD,UAAUG,YAAY,sBAAhDpD,2BAAmDqD,GAAG,CAAC,CAAC/B,OACtD,OAAOA,SAAS,WAAW;YAAEnB,KAAKmB;QAAK,IAAIA,QAE7C;IAEJ,OAAO;QACL4B,SAAS,aAAaD,YAAY,CAAC,CAACA,UAAUC,OAAO,GAAG;QACxD1B,OAAOyB,UAAUzB,KAAK,IAAI;QAC1B4B,cAAcD;QACdG,gBAAgBL,UAAUK,cAAc,IAAI;IAC9C;AACF,EAAC;AAED,OAAO,MAAMC,kBAA6C,CAACC;IACzD,IAAI,CAACA,UAAU,OAAO;IACtB,IAAK,MAAMtC,OAAOsC,SAAU;QAC1B,iCAAiC;QACjCA,QAAQ,CAACtC,IAAI,GAAGlB,0BAA0BwD,QAAQ,CAACtC,IAAI;IACzD;IACA,OAAOsC;AACT,EAAC;AAED,OAAO,MAAMC,gBAGT,CAACC,QAAQtD,cAAc,EAAEC,QAAQ,EAAE;IACrC,IAAI,CAACqD,QAAQ,OAAO;IACpB,OAAO;QACLC,OAAOD,OAAOC,KAAK;QACnBC,aAAaF,OAAOE,WAAW,GAC3B1D,oBAAoBwD,OAAOE,WAAW,EAAExD,cAAcC,YACtDwD;IACN;AACF,EAAC"}