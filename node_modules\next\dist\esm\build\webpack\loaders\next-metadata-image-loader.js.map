{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-metadata-image-loader.ts"], "names": ["existsSync", "promises", "fs", "path", "loaderUtils", "getImageSize", "imageExtMimeTypeMap", "WEBPACK_RESOURCE_QUERIES", "normalizePathSep", "nextMetadataImageLoader", "content", "options", "getOptions", "type", "segment", "pageExtensions", "basePath", "resourcePath", "rootContext", "context", "name", "fileNameBase", "ext", "parse", "useNumericSizes", "extension", "slice", "opts", "contentHash", "interpolateName", "interpolatedName", "isDynamicResource", "includes", "pageSegment", "hash<PERSON><PERSON><PERSON>", "pathnamePrefix", "join", "mod", "Promise", "res", "rej", "loadModule", "err", "_source", "_sourceMap", "module", "exportedFieldsExcludingDefault", "dependencies", "filter", "dep", "constructor", "map", "field", "JSON", "stringify", "metadataImageMeta", "imageSize", "catch", "Error", "imageData", "width", "height", "sizes", "altPath", "dirname", "alt", "readFile", "raw"], "mappings": "AAAA;;CAEC,GAOD,SAASA,UAAU,EAAEC,YAAYC,EAAE,QAAQ,KAAI;AAC/C,OAAOC,UAAU,OAAM;AACvB,OAAOC,iBAAiB,mCAAkC;AAC1D,SAASC,YAAY,QAAQ,kCAAiC;AAC9D,SAASC,mBAAmB,QAAQ,yBAAwB;AAC5D,SAASC,wBAAwB,QAAQ,yBAAwB;AACjE,SAASC,gBAAgB,QAAQ,mDAAkD;AASnF,uBAAuB;AACvB,+EAA+E;AAC/E,eAAeC,wBAAmCC,OAAe;IAC/D,MAAMC,UAAmB,IAAI,CAACC,UAAU;IACxC,MAAM,EAAEC,IAAI,EAAEC,OAAO,EAAEC,cAAc,EAAEC,QAAQ,EAAE,GAAGL;IACpD,MAAM,EAAEM,YAAY,EAAEC,aAAaC,OAAO,EAAE,GAAG,IAAI;IACnD,MAAM,EAAEC,MAAMC,YAAY,EAAEC,GAAG,EAAE,GAAGnB,KAAKoB,KAAK,CAACN;IAC/C,MAAMO,kBAAkBX,SAAS,aAAaA,SAAS;IAEvD,IAAIY,YAAYH,IAAII,KAAK,CAAC;IAC1B,IAAID,cAAc,OAAO;QACvBA,YAAY;IACd;IAEA,MAAME,OAAO;QAAER;QAAST;IAAQ;IAEhC,gCAAgC;IAChC,MAAMkB,cACJf,SAAS,YACL,KACAT,YAAYyB,eAAe,CAAC,IAAI,EAAE,iBAAiBF;IAEzD,MAAMG,mBAAmB1B,YAAYyB,eAAe,CAClD,IAAI,EACJ,gBACAF;IAGF,MAAMI,oBAAoBhB,eAAeiB,QAAQ,CAACP;IAClD,MAAMQ,cAAcF,oBAAoBV,eAAeS;IACvD,MAAMI,YAAYN,cAAc,MAAMA,cAAc;IACpD,MAAMO,iBAAiB3B,iBAAiBL,KAAKiC,IAAI,CAACpB,UAAUF;IAE5D,IAAIiB,mBAAmB;YAcnBM;QAbF,MAAMA,MAAM,MAAM,IAAIC,QAA8B,CAACC,KAAKC;YACxD,IAAI,CAACC,UAAU,CACbxB,cACA,CAACyB,KAAmBC,SAAcC,YAAiBC;gBACjD,IAAIH,KAAK;oBACP,OAAOF,IAAIE;gBACb;gBACAH,IAAIM;YACN;QAEJ;QAEA,MAAMC,iCACJT,EAAAA,oBAAAA,IAAIU,YAAY,qBAAhBV,kBACIW,MAAM,CAAC,CAACC;YACR,OACE;gBACE;gBACA;aACD,CAACjB,QAAQ,CAACiB,IAAIC,WAAW,CAAC9B,IAAI,KAC/B,UAAU6B,OACVA,IAAI7B,IAAI,KAAK;QAEjB,GACC+B,GAAG,CAAC,CAACF;YACJ,OAAOA,IAAI7B,IAAI;QACjB,OAAM,EAAE;QAEZ,0EAA0E;QAC1E,OAAO,CAAC;;MAEN,EAAE0B,+BACCK,GAAG,CAAC,CAACC,QAAU,CAAC,EAAEA,MAAM,KAAK,EAAEA,MAAM,CAAC,EACtChB,IAAI,CAAC,KAAK;WACR,EAAEiB,KAAKC,SAAS,CACrB,4EAA4E;QAC5E,8DAA8D;QAC9D,kEAAkE;QAClE,6EAA6E;QAC7E,WAAW;QACXrC,eAAe,MAAMV,yBAAyBgD,iBAAiB,EAC/D;;;;MAIA,EAAET,+BACCK,GAAG,CAAC,CAACC,QAAU,CAAC,EAAEA,MAAM,GAAG,EAAEA,MAAM,CAAC,EACpChB,IAAI,CAAC,KAAK;;;;;2CAKwB,EAAEiB,KAAKC,SAAS,CACnDnB,gBACA,UAAU,EAAEkB,KAAKC,SAAS,CAACrB,aAAa;;;;;;;;6DAQa,EAAEoB,KAAKC,SAAS,CACjEpB,WACA;;;;UAIF,EACErB,SAAS,aAAaA,SAAS,cAC3B,wDACA,+CACL;;;;;;;;;;;;;;KAcN,CAAC;IACJ;IAEA,MAAM2C,YAAiD,MAAMnD,aAC3DK,SACAe,WACAgC,KAAK,CAAC,CAACf,MAAQA;IAEjB,IAAIc,qBAAqBE,OAAO;QAC9B,MAAMhB,MAAMc;QACZd,IAAItB,IAAI,GAAG;QACX,MAAMsB;IACR;IAEA,MAAMiB,YAA8C;QAClD,GAAIlC,aAAanB,uBAAuB;YACtCO,MAAMP,mBAAmB,CAACmB,UAA8C;QAC1E,CAAC;QACD,GAAID,mBAAmBgC,UAAUI,KAAK,IAAI,QAAQJ,UAAUK,MAAM,IAAI,OAClEL,YACA;YACEM,OACE,sFAAsF;YACtF,uEAAuE;YACvE,+DAA+D;YAC/DrC,cAAc,SACd+B,UAAUI,KAAK,IAAI,QACnBJ,UAAUK,MAAM,IAAI,OAChB,CAAC,EAAEL,UAAUI,KAAK,CAAC,CAAC,EAAEJ,UAAUK,MAAM,CAAC,CAAC,GACxC;QACR,CAAC;IACP;IACA,IAAIhD,SAAS,eAAeA,SAAS,WAAW;QAC9C,MAAMkD,UAAU5D,KAAKiC,IAAI,CACvBjC,KAAK6D,OAAO,CAAC/C,eACbI,eAAe;QAGjB,IAAIrB,WAAW+D,UAAU;YACvBJ,UAAUM,GAAG,GAAG,MAAM/D,GAAGgE,QAAQ,CAACH,SAAS;QAC7C;IACF;IAEA,OAAO,CAAC;;;;sBAIY,EAAEV,KAAKC,SAAS,CAACK,WAAW;yCACT,EAAEN,KAAKC,SAAS,CACnDnB,gBACA,gBAAgB,EAAEkB,KAAKC,SAAS,CAACrB,aAAa;;;;sBAI9B,EAAEoB,KAAKC,SAAS,CAACzC,SAAS,YAAY,KAAKqB,WAAW;;GAEzE,CAAC;AACJ;AAEA,OAAO,MAAMiC,MAAM,KAAI;AACvB,eAAe1D,wBAAuB"}