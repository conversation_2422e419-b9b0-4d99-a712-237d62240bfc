{"version": 3, "sources": ["../../../src/server/base-http/node.ts"], "names": ["SYMBOL_CLEARED_COOKIES", "NEXT_REQUEST_META", "BaseNextRequest", "BaseNextResponse", "NodeNextRequest", "originalRequest", "_req", "url", "cookies", "value", "constructor", "method", "toUpperCase", "headers", "NodeNextResponse", "originalResponse", "_res", "textBody", "undefined", "sent", "finished", "headersSent", "statusCode", "statusMessage", "<PERSON><PERSON><PERSON><PERSON>", "name", "removeHeader", "getHeader<PERSON><PERSON>ues", "values", "<PERSON><PERSON><PERSON><PERSON>", "Array", "isArray", "map", "toString", "<PERSON><PERSON><PERSON><PERSON>", "join", "getHeaders", "append<PERSON><PERSON>er", "currentV<PERSON>ues", "includes", "body", "send", "end"], "mappings": "AAGA,SAASA,sBAAsB,QAAQ,eAAc;AAGrD,SAASC,iBAAiB,QAAQ,kBAAiB;AAGnD,SAASC,eAAe,EAAEC,gBAAgB,QAAQ,UAAS;IAWxDF,qBAAAA;AAHH,OAAO,MAAMG,wBAAwBF;IAKnC,IAAIG,kBAAkB;QACpB,qFAAqF;QACrF,+BAA+B;QAC/B,IAAI,CAACC,IAAI,CAACL,kBAAkB,GAAG,IAAI,CAACA,kBAAkB;QACtD,IAAI,CAACK,IAAI,CAACC,GAAG,GAAG,IAAI,CAACA,GAAG;QACxB,IAAI,CAACD,IAAI,CAACE,OAAO,GAAG,IAAI,CAACA,OAAO;QAChC,OAAO,IAAI,CAACF,IAAI;IAClB;IAEA,IAAID,gBAAgBI,KAAU,EAAE;QAC9B,IAAI,CAACH,IAAI,GAAGG;IACd;IAEAC,YAAoBJ,KAAW;QAC7B,KAAK,CAACA,KAAKK,MAAM,CAAEC,WAAW,IAAIN,KAAKC,GAAG,EAAGD;oBAD3BA;aAjBbO,UAAU,IAAI,CAACP,IAAI,CAACO,OAAO;YAElC,CAACZ,mBAAkB,GAAgB,IAAI,CAACK,IAAI,CAACL,kBAAkB,IAAI,CAAC;IAiBpE;AACF;AAEA,OAAO,MAAMa,yBAAyBX;IAKpC,IAAIY,mBAAmB;QACrB,IAAIf,0BAA0B,IAAI,EAAE;YAClC,IAAI,CAACgB,IAAI,CAAChB,uBAAuB,GAAG,IAAI,CAACA,uBAAuB;QAClE;QAEA,OAAO,IAAI,CAACgB,IAAI;IAClB;IAEAN,YACUM,KACR;QACA,KAAK,CAACA;oBAFEA;aAbFC,WAA+BC;IAgBvC;IAEA,IAAIC,OAAO;QACT,OAAO,IAAI,CAACH,IAAI,CAACI,QAAQ,IAAI,IAAI,CAACJ,IAAI,CAACK,WAAW;IACpD;IAEA,IAAIC,aAAa;QACf,OAAO,IAAI,CAACN,IAAI,CAACM,UAAU;IAC7B;IAEA,IAAIA,WAAWb,KAAa,EAAE;QAC5B,IAAI,CAACO,IAAI,CAACM,UAAU,GAAGb;IACzB;IAEA,IAAIc,gBAAgB;QAClB,OAAO,IAAI,CAACP,IAAI,CAACO,aAAa;IAChC;IAEA,IAAIA,cAAcd,KAAa,EAAE;QAC/B,IAAI,CAACO,IAAI,CAACO,aAAa,GAAGd;IAC5B;IAEAe,UAAUC,IAAY,EAAEhB,KAAwB,EAAQ;QACtD,IAAI,CAACO,IAAI,CAACQ,SAAS,CAACC,MAAMhB;QAC1B,OAAO,IAAI;IACb;IAEAiB,aAAaD,IAAY,EAAQ;QAC/B,IAAI,CAACT,IAAI,CAACU,YAAY,CAACD;QACvB,OAAO,IAAI;IACb;IAEAE,gBAAgBF,IAAY,EAAwB;QAClD,MAAMG,SAAS,IAAI,CAACZ,IAAI,CAACa,SAAS,CAACJ;QAEnC,IAAIG,WAAWV,WAAW,OAAOA;QAEjC,OAAO,AAACY,CAAAA,MAAMC,OAAO,CAACH,UAAUA,SAAS;YAACA;SAAO,AAAD,EAAGI,GAAG,CAAC,CAACvB,QACtDA,MAAMwB,QAAQ;IAElB;IAEAC,UAAUT,IAAY,EAAW;QAC/B,OAAO,IAAI,CAACT,IAAI,CAACkB,SAAS,CAACT;IAC7B;IAEAI,UAAUJ,IAAY,EAAsB;QAC1C,MAAMG,SAAS,IAAI,CAACD,eAAe,CAACF;QACpC,OAAOK,MAAMC,OAAO,CAACH,UAAUA,OAAOO,IAAI,CAAC,OAAOjB;IACpD;IAEAkB,aAAkC;QAChC,OAAO,IAAI,CAACpB,IAAI,CAACoB,UAAU;IAC7B;IAEAC,aAAaZ,IAAY,EAAEhB,KAAa,EAAQ;QAC9C,MAAM6B,gBAAgB,IAAI,CAACX,eAAe,CAACF,SAAS,EAAE;QAEtD,IAAI,CAACa,cAAcC,QAAQ,CAAC9B,QAAQ;YAClC,IAAI,CAACO,IAAI,CAACQ,SAAS,CAACC,MAAM;mBAAIa;gBAAe7B;aAAM;QACrD;QAEA,OAAO,IAAI;IACb;IAEA+B,KAAK/B,KAAa,EAAE;QAClB,IAAI,CAACQ,QAAQ,GAAGR;QAChB,OAAO,IAAI;IACb;IAEAgC,OAAO;QACL,IAAI,CAACzB,IAAI,CAAC0B,GAAG,CAAC,IAAI,CAACzB,QAAQ;IAC7B;AACF"}