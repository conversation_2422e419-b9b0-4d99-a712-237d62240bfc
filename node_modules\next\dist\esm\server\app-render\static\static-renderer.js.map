{"version": 3, "sources": ["../../../../src/server/app-render/static/static-renderer.ts"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "options", "prerender", "process", "env", "__NEXT_EXPERIMENTAL_REACT", "require", "render", "children", "prelude", "postponed", "stream", "StaticResumeRenderer", "resume", "<PERSON><PERSON><PERSON><PERSON>", "renderToReadableStream", "createStatic<PERSON><PERSON><PERSON>", "ppr", "isStaticGeneration", "streamOptions", "onError", "onHeaders", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nonce", "bootstrapScripts", "formState"], "mappings": "AAeA,MAAMA;IAMJC,YAA6BC,QAA2B;uBAA3BA;aAJZC,YAAaC,QAAQC,GAAG,CAACC,yBAAyB,GAC/DC,QAAQ,yBAAyBJ,SAAS,GAC1C;IAEqD;IAEzD,MAAaK,OAAOC,QAAqB,EAAE;QACzC,MAAM,EAAEC,OAAO,EAAEC,SAAS,EAAE,GAAG,MAAM,IAAI,CAACR,SAAS,CAACM,UAAU,IAAI,CAACP,OAAO;QAE1E,OAAO;YAAEU,QAAQF;YAASC;QAAU;IACtC;AACF;AAEA,MAAME;IAIJZ,YACmBU,WACAT,QACjB;yBAFiBS;uBACAT;aALFY,SAASP,QAAQ,yBAC/BO,MAAM;IAKN;IAEH,MAAaN,OAAOC,QAAqB,EAAE;QACzC,MAAMG,SAAS,MAAM,IAAI,CAACE,MAAM,CAACL,UAAU,IAAI,CAACE,SAAS,EAAE,IAAI,CAACT,OAAO;QAEvE,OAAO;YAAEU;QAAO;IAClB;AACF;AAEA,OAAO,MAAMG;IAIXd,YAA6BC,QAAwC;uBAAxCA;aAHZc,yBAAyBT,QAAQ,yBAC/CS,sBAAsB;IAE6C;IAEtE,MAAaR,OAAOC,QAAqB,EAAyB;QAChE,MAAMG,SAAS,MAAM,IAAI,CAACI,sBAAsB,CAACP,UAAU,IAAI,CAACP,OAAO;QACvE,OAAO;YAAEU;QAAO;IAClB;AACF;AA+CA,OAAO,SAASK,qBAAqB,EACnCC,GAAG,EACHC,kBAAkB,EAClBR,SAAS,EACTS,eAAe,EACbC,OAAO,EACPC,SAAS,EACTC,gBAAgB,EAChBC,KAAK,EACLC,gBAAgB,EAChBC,SAAS,EACV,EACO;IACR,IAAIR,KAAK;QACP,IAAIC,oBAAoB;YACtB,OAAO,IAAInB,eAAe;gBACxBqB;gBACAC;gBACAC;gBACAE;YACF;QACF;QAEA,IAAId,WAAW;YACb,OAAO,IAAIE,qBAAqBF,WAAW;gBACzCU;gBACAG;YACF;QACF;IACF;IAEA,OAAO,IAAIT,eAAe;QACxBM;QACAC;QACAC;QACAC;QACAC;QACAC;IACF;AACF"}