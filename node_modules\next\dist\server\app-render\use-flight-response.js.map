{"version": 3, "sources": ["../../../src/server/app-render/use-flight-response.tsx"], "names": ["useFlightResponse", "isEdgeRuntime", "process", "env", "NEXT_RUNTIME", "INLINE_FLIGHT_PAYLOAD_BOOTSTRAP", "INLINE_FLIGHT_PAYLOAD_DATA", "INLINE_FLIGHT_PAYLOAD_FORM_STATE", "createFlightTransformer", "nonce", "formState", "startScriptTag", "JSON", "stringify", "TransformStream", "start", "controller", "enqueue", "htmlEscapeJsonString", "transform", "chunk", "scripts", "writable", "flightStream", "clientReferenceManifest", "flightResponseRef", "current", "createFromReadableStream", "TURBOPACK", "require", "renderStream", "forwardStream", "tee", "res", "ssrManifest", "moduleLoading", "moduleMap", "edgeSSRModuleMapping", "ssrModuleMapping", "pipeThrough", "createDecodeTransformStream", "createEncodeTransformStream", "pipeTo", "finally", "catch", "err", "console", "error"], "mappings": ";;;;+BAgDgBA;;;eAAAA;;;4BA7CqB;8BAI9B;AAEP,MAAMC,gBAAgBC,QAAQC,GAAG,CAACC,YAAY,KAAK;AAEnD,MAAMC,kCAAkC;AACxC,MAAMC,6BAA6B;AACnC,MAAMC,mCAAmC;AAEzC,SAASC,wBACPC,KAAyB,EACzBC,SAAyB;IAEzB,MAAMC,iBAAiBF,QACnB,CAAC,cAAc,EAAEG,KAAKC,SAAS,CAACJ,OAAO,CAAC,CAAC,GACzC;IAEJ,OAAO,IAAIK,gBAAgC;QACzC,oCAAoC;QACpCC,OAAMC,UAAU;YACdA,WAAWC,OAAO,CAChB,CAAC,EAAEN,eAAe,uCAAuC,EAAEO,IAAAA,gCAAoB,EAC7EN,KAAKC,SAAS,CAAC;gBAACR;aAAgC,GAChD,qBAAqB,EAAEa,IAAAA,gCAAoB,EAC3CN,KAAKC,SAAS,CAAC;gBAACN;gBAAkCG;aAAU,GAC5D,UAAU,CAAC;QAEjB;QACAS,WAAUC,KAAK,EAAEJ,UAAU;YACzB,MAAMK,UAAU,CAAC,EAAEV,eAAe,mBAAmB,EAAEO,IAAAA,gCAAoB,EACzEN,KAAKC,SAAS,CAAC;gBAACP;gBAA4Bc;aAAM,GAClD,UAAU,CAAC;YAEbJ,WAAWC,OAAO,CAACI;QACrB;IACF;AACF;AAMO,SAASrB,kBACdsB,QAAoC,EACpCC,YAAwC,EACxCC,uBAAgD,EAChDC,iBAAoC,EACpCf,SAAqB,EACrBD,KAAc;IAEd,IAAIgB,kBAAkBC,OAAO,KAAK,MAAM;QACtC,OAAOD,kBAAkBC,OAAO;IAClC;IACA,wGAAwG;IACxG,IAAIC;IACJ,uGAAuG;IACvG,IAAIzB,QAAQC,GAAG,CAACyB,SAAS,EAAE;QACzBD,2BACE,6DAA6D;QAC7DE,QAAQ,0CAA0CF,wBAAwB;IAC9E,OAAO;QACLA,2BACE,6DAA6D;QAC7DE,QAAQ,wCAAwCF,wBAAwB;IAC5E;IAEA,MAAM,CAACG,cAAcC,cAAc,GAAGR,aAAaS,GAAG;IACtD,MAAMC,MAAMN,yBAAyBG,cAAc;QACjDI,aAAa;YACXC,eAAeX,wBAAwBW,aAAa;YACpDC,WAAWnC,gBACPuB,wBAAwBa,oBAAoB,GAC5Cb,wBAAwBc,gBAAgB;QAC9C;QACA7B;IACF;IACAgB,kBAAkBC,OAAO,GAAGO;IAE5BF,cACGQ,WAAW,CAACC,IAAAA,yCAA2B,KACvCD,WAAW,CAAC/B,wBAAwBC,OAAOC,YAC3C6B,WAAW,CAACE,IAAAA,yCAA2B,KACvCC,MAAM,CAACpB,UACPqB,OAAO,CAAC;QACP,mEAAmE;QACnE,gBAAgB;QAChBlB,kBAAkBC,OAAO,GAAG;IAC9B,GACCkB,KAAK,CAAC,CAACC;QACNC,QAAQC,KAAK,CAAC,kDAAkDF;IAClE;IAEF,OAAOZ;AACT"}