'use client'

import { useState, useEffect, useRef } from 'react'

interface PerformanceMetrics {
  fps: number
  memoryUsage: number
  renderTime: number
  networkLatency: number
  cpuUsage: number
  gpuUsage: number
  frameDrops: number
  totalFrames: number
}

interface PerformanceMonitorProps {
  isVisible: boolean
  onToggle: () => void
}

export default function PerformanceMonitor({ isVisible, onToggle }: PerformanceMonitorProps) {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    fps: 0,
    memoryUsage: 0,
    renderTime: 0,
    networkLatency: 0,
    cpuUsage: 0,
    gpuUsage: 0,
    frameDrops: 0,
    totalFrames: 0
  })

  const [isRecording, setIsRecording] = useState(false)
  const [performanceHistory, setPerformanceHistory] = useState<PerformanceMetrics[]>([])
  const frameCountRef = useRef(0)
  const lastTimeRef = useRef(performance.now())
  const fpsHistoryRef = useRef<number[]>([])
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    if (isVisible) {
      startMonitoring()
    } else {
      stopMonitoring()
    }

    return () => stopMonitoring()
  }, [isVisible])

  const startMonitoring = () => {
    intervalRef.current = setInterval(() => {
      updateMetrics()
    }, 1000) // 每秒更新一次

    // 开始FPS监控
    requestAnimationFrame(measureFPS)
  }

  const stopMonitoring = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = null
    }
  }

  const measureFPS = (currentTime: number) => {
    frameCountRef.current++
    
    if (currentTime - lastTimeRef.current >= 1000) {
      const fps = Math.round((frameCountRef.current * 1000) / (currentTime - lastTimeRef.current))
      
      fpsHistoryRef.current.push(fps)
      if (fpsHistoryRef.current.length > 60) {
        fpsHistoryRef.current.shift()
      }

      frameCountRef.current = 0
      lastTimeRef.current = currentTime
    }

    if (isVisible) {
      requestAnimationFrame(measureFPS)
    }
  }

  const updateMetrics = async () => {
    try {
      const newMetrics: PerformanceMetrics = {
        fps: fpsHistoryRef.current.length > 0 ? fpsHistoryRef.current[fpsHistoryRef.current.length - 1] : 0,
        memoryUsage: await getMemoryUsage(),
        renderTime: await getRenderTime(),
        networkLatency: await getNetworkLatency(),
        cpuUsage: await getCPUUsage(),
        gpuUsage: await getGPUUsage(),
        frameDrops: getFrameDrops(),
        totalFrames: getTotalFrames()
      }

      setMetrics(newMetrics)

      if (isRecording) {
        setPerformanceHistory(prev => [...prev.slice(-299), newMetrics])
      }
    } catch (error) {
      console.error('Failed to update performance metrics:', error)
    }
  }

  const getMemoryUsage = async (): Promise<number> => {
    if ('memory' in performance) {
      const memory = (performance as any).memory
      return Math.round((memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100)
    }
    return 0
  }

  const getRenderTime = async (): Promise<number> => {
    // 模拟渲染时间测量
    const start = performance.now()
    await new Promise(resolve => requestAnimationFrame(resolve))
    return Math.round(performance.now() - start)
  }

  const getNetworkLatency = async (): Promise<number> => {
    try {
      const start = performance.now()
      await fetch('/api/ping', { method: 'HEAD' })
      return Math.round(performance.now() - start)
    } catch {
      return 0
    }
  }

  const getCPUUsage = async (): Promise<number> => {
    // 简化的CPU使用率估算
    const start = performance.now()
    let iterations = 0
    const endTime = start + 10 // 10ms测试

    while (performance.now() < endTime) {
      iterations++
    }

    // 基于迭代次数估算CPU负载
    const baselineIterations = 100000
    return Math.min(100, Math.max(0, 100 - (iterations / baselineIterations) * 100))
  }

  const getGPUUsage = async (): Promise<number> => {
    // GPU使用率估算（基于WebGL上下文）
    try {
      const canvas = document.createElement('canvas')
      const gl = canvas.getContext('webgl2') || canvas.getContext('webgl')
      
      if (gl) {
        const ext = gl.getExtension('WEBGL_debug_renderer_info')
        if (ext) {
          // 简化的GPU负载估算
          return Math.random() * 30 + 20 // 模拟20-50%的GPU使用率
        }
      }
    } catch {
      // GPU信息不可用
    }
    return 0
  }

  const getFrameDrops = (): number => {
    // 计算掉帧数
    const avgFPS = fpsHistoryRef.current.reduce((sum, fps) => sum + fps, 0) / fpsHistoryRef.current.length
    const targetFPS = 60
    return Math.max(0, Math.round((targetFPS - avgFPS) * fpsHistoryRef.current.length))
  }

  const getTotalFrames = (): number => {
    return fpsHistoryRef.current.reduce((sum, fps) => sum + fps, 0)
  }

  const getPerformanceLevel = (): { level: string; color: string; description: string } => {
    const { fps, memoryUsage, cpuUsage } = metrics
    
    if (fps >= 55 && memoryUsage < 70 && cpuUsage < 60) {
      return { level: '优秀', color: 'text-green-600', description: '性能表现优秀' }
    } else if (fps >= 45 && memoryUsage < 80 && cpuUsage < 75) {
      return { level: '良好', color: 'text-blue-600', description: '性能表现良好' }
    } else if (fps >= 30 && memoryUsage < 90 && cpuUsage < 85) {
      return { level: '一般', color: 'text-yellow-600', description: '性能表现一般' }
    } else {
      return { level: '较差', color: 'text-red-600', description: '性能需要优化' }
    }
  }

  const exportPerformanceData = () => {
    const data = {
      timestamp: new Date().toISOString(),
      currentMetrics: metrics,
      history: performanceHistory,
      summary: {
        avgFPS: performanceHistory.reduce((sum, m) => sum + m.fps, 0) / performanceHistory.length,
        maxMemory: Math.max(...performanceHistory.map(m => m.memoryUsage)),
        avgLatency: performanceHistory.reduce((sum, m) => sum + m.networkLatency, 0) / performanceHistory.length
      }
    }

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `performance-report-${Date.now()}.json`
    a.click()
    URL.revokeObjectURL(url)
  }

  const performanceLevel = getPerformanceLevel()

  if (!isVisible) {
    return (
      <button
        onClick={onToggle}
        className="fixed bottom-4 right-4 p-3 bg-gray-800 text-white rounded-full shadow-lg hover:bg-gray-700 transition-colors z-50"
        title="性能监控"
      >
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      </button>
    )
  }

  return (
    <div className="fixed bottom-4 right-4 w-80 bg-white rounded-xl shadow-2xl border border-gray-200 z-50">
      {/* 头部 */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
          <h3 className="font-semibold text-gray-800">性能监控</h3>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setIsRecording(!isRecording)}
            className={`px-2 py-1 text-xs rounded ${
              isRecording ? 'bg-red-100 text-red-600' : 'bg-gray-100 text-gray-600'
            }`}
          >
            {isRecording ? '停止记录' : '开始记录'}
          </button>
          <button
            onClick={onToggle}
            className="p-1 text-gray-400 hover:text-gray-600 rounded"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>

      {/* 性能等级 */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">整体性能</span>
          <div className="text-right">
            <div className={`font-semibold ${performanceLevel.color}`}>
              {performanceLevel.level}
            </div>
            <div className="text-xs text-gray-500">
              {performanceLevel.description}
            </div>
          </div>
        </div>
      </div>

      {/* 指标网格 */}
      <div className="p-4 space-y-3">
        <div className="grid grid-cols-2 gap-3">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{metrics.fps}</div>
            <div className="text-xs text-gray-500">FPS</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{metrics.memoryUsage}%</div>
            <div className="text-xs text-gray-500">内存</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">{metrics.networkLatency}ms</div>
            <div className="text-xs text-gray-500">延迟</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">{metrics.cpuUsage}%</div>
            <div className="text-xs text-gray-500">CPU</div>
          </div>
        </div>

        {/* 详细信息 */}
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-600">渲染时间</span>
            <span className="font-medium">{metrics.renderTime}ms</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">GPU使用率</span>
            <span className="font-medium">{metrics.gpuUsage}%</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">掉帧数</span>
            <span className="font-medium">{metrics.frameDrops}</span>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex space-x-2 pt-2">
          <button
            onClick={exportPerformanceData}
            disabled={performanceHistory.length === 0}
            className="flex-1 px-3 py-2 text-xs bg-blue-100 text-blue-600 rounded hover:bg-blue-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            导出报告
          </button>
          <button
            onClick={() => setPerformanceHistory([])}
            className="flex-1 px-3 py-2 text-xs bg-gray-100 text-gray-600 rounded hover:bg-gray-200 transition-colors"
          >
            清除历史
          </button>
        </div>
      </div>
    </div>
  )
}
