{"version": 3, "sources": ["../../../src/server/app-render/use-flight-response.tsx"], "names": ["htmlEscapeJsonString", "createDecodeTransformStream", "createEncodeTransformStream", "isEdgeRuntime", "process", "env", "NEXT_RUNTIME", "INLINE_FLIGHT_PAYLOAD_BOOTSTRAP", "INLINE_FLIGHT_PAYLOAD_DATA", "INLINE_FLIGHT_PAYLOAD_FORM_STATE", "createFlightTransformer", "nonce", "formState", "startScriptTag", "JSON", "stringify", "TransformStream", "start", "controller", "enqueue", "transform", "chunk", "scripts", "useFlightResponse", "writable", "flightStream", "clientReferenceManifest", "flightResponseRef", "current", "createFromReadableStream", "TURBOPACK", "require", "renderStream", "forwardStream", "tee", "res", "ssrManifest", "moduleLoading", "moduleMap", "edgeSSRModuleMapping", "ssrModuleMapping", "pipeThrough", "pipeTo", "finally", "catch", "err", "console", "error"], "mappings": "AAGA,SAASA,oBAAoB,QAAQ,gBAAe;AACpD,SACEC,2BAA2B,EAC3BC,2BAA2B,QACtB,gCAA+B;AAEtC,MAAMC,gBAAgBC,QAAQC,GAAG,CAACC,YAAY,KAAK;AAEnD,MAAMC,kCAAkC;AACxC,MAAMC,6BAA6B;AACnC,MAAMC,mCAAmC;AAEzC,SAASC,wBACPC,KAAyB,EACzBC,SAAyB;IAEzB,MAAMC,iBAAiBF,QACnB,CAAC,cAAc,EAAEG,KAAKC,SAAS,CAACJ,OAAO,CAAC,CAAC,GACzC;IAEJ,OAAO,IAAIK,gBAAgC;QACzC,oCAAoC;QACpCC,OAAMC,UAAU;YACdA,WAAWC,OAAO,CAChB,CAAC,EAAEN,eAAe,uCAAuC,EAAEb,qBACzDc,KAAKC,SAAS,CAAC;gBAACR;aAAgC,GAChD,qBAAqB,EAAEP,qBACvBc,KAAKC,SAAS,CAAC;gBAACN;gBAAkCG;aAAU,GAC5D,UAAU,CAAC;QAEjB;QACAQ,WAAUC,KAAK,EAAEH,UAAU;YACzB,MAAMI,UAAU,CAAC,EAAET,eAAe,mBAAmB,EAAEb,qBACrDc,KAAKC,SAAS,CAAC;gBAACP;gBAA4Ba;aAAM,GAClD,UAAU,CAAC;YAEbH,WAAWC,OAAO,CAACG;QACrB;IACF;AACF;AAEA;;;CAGC,GACD,OAAO,SAASC,kBACdC,QAAoC,EACpCC,YAAwC,EACxCC,uBAAgD,EAChDC,iBAAoC,EACpCf,SAAqB,EACrBD,KAAc;IAEd,IAAIgB,kBAAkBC,OAAO,KAAK,MAAM;QACtC,OAAOD,kBAAkBC,OAAO;IAClC;IACA,wGAAwG;IACxG,IAAIC;IACJ,uGAAuG;IACvG,IAAIzB,QAAQC,GAAG,CAACyB,SAAS,EAAE;QACzBD,2BACE,6DAA6D;QAC7DE,QAAQ,0CAA0CF,wBAAwB;IAC9E,OAAO;QACLA,2BACE,6DAA6D;QAC7DE,QAAQ,wCAAwCF,wBAAwB;IAC5E;IAEA,MAAM,CAACG,cAAcC,cAAc,GAAGR,aAAaS,GAAG;IACtD,MAAMC,MAAMN,yBAAyBG,cAAc;QACjDI,aAAa;YACXC,eAAeX,wBAAwBW,aAAa;YACpDC,WAAWnC,gBACPuB,wBAAwBa,oBAAoB,GAC5Cb,wBAAwBc,gBAAgB;QAC9C;QACA7B;IACF;IACAgB,kBAAkBC,OAAO,GAAGO;IAE5BF,cACGQ,WAAW,CAACxC,+BACZwC,WAAW,CAAC/B,wBAAwBC,OAAOC,YAC3C6B,WAAW,CAACvC,+BACZwC,MAAM,CAAClB,UACPmB,OAAO,CAAC;QACP,mEAAmE;QACnE,gBAAgB;QAChBhB,kBAAkBC,OAAO,GAAG;IAC9B,GACCgB,KAAK,CAAC,CAACC;QACNC,QAAQC,KAAK,CAAC,kDAAkDF;IAClE;IAEF,OAAOV;AACT"}