'use client'

import { useState, useEffect, useRef } from 'react'
import Header from '@/components/Header'
import Live2<PERSON><PERSON>iewer from '@/components/Live2DViewer'
import ChatPanel from '@/components/ChatPanel'
import ControlPanel from '@/components/ControlPanel'
import VideoChat from '@/components/VideoChat'
import LoadingScreen from '@/components/LoadingScreen'
import SettingsPanel from '@/components/SettingsPanel'
import ModelSelector from '@/components/ModelSelector'
import PerformanceMonitor from '@/components/PerformanceMonitor'
import { FaceData, Message } from '@/types'
import {
  RealtimeInteractionManager,
  createRealtimeInteractionManager,
  DEFAULT_REALTIME_INTERACTION_CONFIG
} from '@/lib/realtime-interaction-manager'
import { performanceOptimizer } from '@/utils/performance-optimizer'
import { compatibilityTester } from '@/utils/compatibility-tester'

export default function Home() {
  const [isLoading, setIsLoading] = useState(true)
  const [isVideoEnabled, setIsVideoEnabled] = useState(false)
  const [selectedModel, setSelectedModel] = useState('xiaoli')
  const [currentExpression, setCurrentExpression] = useState('default')
  const [faceData, setFaceData] = useState<FaceData | null>(null)
  const [chatMessages, setChatMessages] = useState<Message[]>([])
  const [isSettingsOpen, setIsSettingsOpen] = useState(false)
  const [isModelSelectorOpen, setIsModelSelectorOpen] = useState(false)
  const [isPerformanceMonitorVisible, setIsPerformanceMonitorVisible] = useState(false)
  const [interactionManager, setInteractionManager] = useState<RealtimeInteractionManager | null>(null)

  // 设置状态
  const [settings, setSettings] = useState({
    volume: 80,
    sensitivity: 70,
    autoExpression: true,
    lipSync: true,
    eyeTracking: true,
    voiceEnabled: true,
    selectedVoice: 'zh-CN-XiaoxiaoNeural',
    speechRate: 1.0,
    speechPitch: 1.0
  })

  const interactionManagerRef = useRef<RealtimeInteractionManager | null>(null)

  useEffect(() => {
    // 初始化应用程序
    const initializeApp = async () => {
      try {
        // 运行兼容性测试
        const compatibilityResults = await compatibilityTester.runFullCompatibilityTest()
        console.log('Compatibility test results:', compatibilityResults)

        // 获取推荐设置
        const recommendedSettings = compatibilityTester.getRecommendedSettings()
        console.log('Recommended settings:', recommendedSettings)

        // 启用性能优化
        performanceOptimizer.optimizeImages()
        performanceOptimizer.optimizeNetwork()

        // 初始化实时交互管理器
        const manager = createRealtimeInteractionManager(
          DEFAULT_REALTIME_INTERACTION_CONFIG,
          {
            onExpressionChanged: (expression, intensity) => {
              setCurrentExpression(expression)
            },
            onParameterUpdated: (parameterId, value) => {
              // 更新Live2D参数
              console.log(`Parameter updated: ${parameterId} = ${value}`)
            },
            onMouthShapeChanged: (shape, intensity) => {
              // 更新嘴部形状
              console.log(`Mouth shape: ${shape} (${intensity})`)
            },
            onGestureDetected: (gesture, confidence) => {
              console.log(`Gesture detected: ${gesture} (${confidence})`)
            },
            onEmotionChanged: (emotion, intensity) => {
              console.log(`Emotion changed: ${emotion} (${intensity})`)
            },
            onInteractionTriggered: (type, data) => {
              console.log(`Interaction: ${type}`, data)
            }
          }
        )

        await manager.initialize()
        manager.start()

        setInteractionManager(manager)
        interactionManagerRef.current = manager
      } catch (error) {
        console.error('Failed to initialize app:', error)
      }
    }

    // 模拟初始化加载
    const timer = setTimeout(() => {
      setIsLoading(false)
      initializeApp()
    }, 2000)

    return () => {
      clearTimeout(timer)
      if (interactionManagerRef.current) {
        interactionManagerRef.current.dispose()
      }
      performanceOptimizer.dispose()
    }
  }, [])

  const handleSendMessage = async (message: string) => {
    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: message,
      timestamp: new Date()
    }

    setChatMessages(prev => [...prev, userMessage])

    // 使用实时交互管理器处理语音
    if (interactionManager) {
      interactionManager.processSpeech(message)
    }
  }

  const handleMessageReceived = (message: Message) => {
    setChatMessages(prev => [...prev, message])
  }

  const handleVideoToggle = () => {
    setIsVideoEnabled(!isVideoEnabled)
  }

  const handleModelChange = (modelId: string) => {
    setSelectedModel(modelId)
  }

  const handleFaceData = (newFaceData: FaceData) => {
    setFaceData(newFaceData)

    // 使用实时交互管理器处理面部数据
    if (interactionManager && settings.autoExpression) {
      interactionManager.updateFaceData(newFaceData)
    }
  }

  const handleModelLoaded = (modelId: string) => {
    console.log(`Model loaded: ${modelId}`)
  }

  const handleModelError = (error: Error) => {
    console.error('Model error:', error)
  }

  const handleSettingsChange = (newSettings: any) => {
    setSettings(newSettings)

    // 更新实时交互管理器配置
    if (interactionManager) {
      interactionManager.updateConfig({
        enableExpressionSync: newSettings.autoExpression,
        enableLipSync: newSettings.lipSync,
        interactionSensitivity: newSettings.sensitivity / 100
      })
    }
  }

  if (isLoading) {
    return <LoadingScreen />
  }

  return (
    <main className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
      <Header />
      
      <div className="container mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-120px)]">
          {/* Live2D 数字人显示区域 */}
          <div className="lg:col-span-2 bg-white rounded-xl shadow-lg overflow-hidden">
            <div className="h-full relative">
              <Live2DViewer
                modelId={selectedModel}
                isVideoEnabled={isVideoEnabled}
                faceData={faceData}
                currentExpression={currentExpression}
                onModelLoaded={handleModelLoaded}
                onModelError={handleModelError}
              />

              {isVideoEnabled && (
                <div className="absolute top-4 right-4 w-48 h-36">
                  <VideoChat
                    onFaceData={handleFaceData}
                    isEnabled={isVideoEnabled}
                  />
                </div>
              )}
            </div>
          </div>

          {/* 右侧控制面板 */}
          <div className="space-y-6">
            {/* 控制面板 */}
            <ControlPanel
              isVideoEnabled={isVideoEnabled}
              onVideoToggle={handleVideoToggle}
              selectedModel={selectedModel}
              onModelChange={handleModelChange}
              onSettingsClick={() => setIsSettingsOpen(true)}
              onModelSelectorClick={() => setIsModelSelectorOpen(true)}
            />

            {/* 聊天面板 */}
            <ChatPanel
              messages={chatMessages}
              onSendMessage={handleSendMessage}
              onMessageReceived={handleMessageReceived}
            />
          </div>
        </div>
      </div>

      {/* 设置面板 */}
      <SettingsPanel
        isOpen={isSettingsOpen}
        onClose={() => setIsSettingsOpen(false)}
        settings={settings}
        onSettingsChange={handleSettingsChange}
      />

      {/* 模型选择器 */}
      <ModelSelector
        isOpen={isModelSelectorOpen}
        onClose={() => setIsModelSelectorOpen(false)}
        currentModel={selectedModel}
        onModelSelect={handleModelChange}
      />

      {/* 性能监控 */}
      <PerformanceMonitor
        isVisible={isPerformanceMonitorVisible}
        onToggle={() => setIsPerformanceMonitorVisible(!isPerformanceMonitorVisible)}
      />
    </main>
  )
}
