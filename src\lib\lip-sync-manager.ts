// 语音驱动嘴部动画管理器
import { createAudioProcessor } from '@/utils/speech'

/**
 * 嘴部动画配置
 */
export interface LipSyncConfig {
  enabled: boolean
  sensitivity: number
  smoothing: number
  volumeThreshold: number
  frequencyAnalysis: boolean
  phonemeMapping: boolean
}

/**
 * 音素映射
 */
export interface PhonemeMapping {
  phoneme: string
  mouthShape: string
  duration: number
}

/**
 * 嘴部动画事件
 */
export interface LipSyncEvents {
  onMouthShapeChanged?: (shape: string, intensity: number) => void
  onVolumeChanged?: (volume: number) => void
  onSpeechDetected?: (isDetected: boolean) => void
}

/**
 * 语音驱动嘴部动画管理器
 */
export class LipSyncManager {
  private config: LipSyncConfig
  private events: LipSyncEvents
  private audioProcessor: any = null
  private isActive: boolean = false
  private animationFrame: number | null = null
  private currentMouthShape: string = 'closed'
  private volumeHistory: number[] = []
  private speechDetected: boolean = false

  constructor(config: LipSyncConfig, events: LipSyncEvents = {}) {
    this.config = config
    this.events = events
  }

  /**
   * 初始化嘴部动画系统
   */
  async initialize(): Promise<void> {
    try {
      this.audioProcessor = createAudioProcessor()
      await this.audioProcessor.initialize()
      console.log('Lip sync manager initialized')
    } catch (error) {
      console.error('Failed to initialize lip sync manager:', error)
      throw error
    }
  }

  /**
   * 开始嘴部动画
   */
  start(): void {
    if (!this.audioProcessor || this.isActive) return

    this.isActive = true
    this.startAnalysis()
    console.log('Lip sync started')
  }

  /**
   * 停止嘴部动画
   */
  stop(): void {
    this.isActive = false
    
    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame)
      this.animationFrame = null
    }

    this.currentMouthShape = 'closed'
    this.events.onMouthShapeChanged?.('closed', 0)
    console.log('Lip sync stopped')
  }

  /**
   * 开始音频分析
   */
  private startAnalysis(): void {
    const analyze = () => {
      if (!this.isActive || !this.audioProcessor) return

      try {
        // 获取音量级别
        const volumeLevel = this.audioProcessor.getVolumeLevel()
        this.updateVolumeHistory(volumeLevel)

        // 检测语音活动
        const speechActive = this.detectSpeechActivity(volumeLevel)
        
        if (speechActive !== this.speechDetected) {
          this.speechDetected = speechActive
          this.events.onSpeechDetected?.(speechActive)
        }

        // 更新嘴部形状
        if (this.config.enabled) {
          this.updateMouthShape(volumeLevel)
        }

        // 通知音量变化
        this.events.onVolumeChanged?.(volumeLevel)

        // 继续分析
        this.animationFrame = requestAnimationFrame(analyze)
      } catch (error) {
        console.error('Audio analysis error:', error)
      }
    }

    this.animationFrame = requestAnimationFrame(analyze)
  }

  /**
   * 更新音量历史
   */
  private updateVolumeHistory(volume: number): void {
    this.volumeHistory.push(volume)
    
    // 保持历史记录大小
    const maxHistory = Math.max(10, Math.floor(this.config.smoothing * 30))
    if (this.volumeHistory.length > maxHistory) {
      this.volumeHistory.shift()
    }
  }

  /**
   * 检测语音活动
   */
  private detectSpeechActivity(volume: number): boolean {
    return volume > this.config.volumeThreshold
  }

  /**
   * 更新嘴部形状
   */
  private updateMouthShape(volume: number): void {
    // 计算平滑后的音量
    const smoothedVolume = this.getSmoothVolume()
    
    // 应用敏感度
    const adjustedVolume = smoothedVolume * this.config.sensitivity

    // 确定嘴部形状
    let mouthShape = 'closed'
    let intensity = 0

    if (adjustedVolume > this.config.volumeThreshold) {
      if (this.config.frequencyAnalysis) {
        // 基于频率分析的嘴部形状
        mouthShape = this.analyzeFrequencyForMouthShape()
      } else {
        // 基于音量的简单嘴部形状
        mouthShape = this.getVolumeBasedMouthShape(adjustedVolume)
      }
      
      intensity = Math.min(adjustedVolume * 2, 1.0)
    }

    // 更新嘴部形状
    if (mouthShape !== this.currentMouthShape) {
      this.currentMouthShape = mouthShape
      this.events.onMouthShapeChanged?.(mouthShape, intensity)
    }
  }

  /**
   * 获取平滑后的音量
   */
  private getSmoothVolume(): number {
    if (this.volumeHistory.length === 0) return 0

    // 计算加权平均值，最近的值权重更高
    let weightedSum = 0
    let totalWeight = 0

    for (let i = 0; i < this.volumeHistory.length; i++) {
      const weight = (i + 1) / this.volumeHistory.length
      weightedSum += this.volumeHistory[i] * weight
      totalWeight += weight
    }

    return weightedSum / totalWeight
  }

  /**
   * 基于频率分析的嘴部形状
   */
  private analyzeFrequencyForMouthShape(): string {
    if (!this.audioProcessor) return 'closed'

    try {
      const frequencyData = this.audioProcessor.getFrequencyData()
      if (!frequencyData) return 'closed'

      // 分析不同频率范围
      const lowFreq = this.getFrequencyRange(frequencyData, 0, 85)      // 低频 (元音 A, O)
      const midFreq = this.getFrequencyRange(frequencyData, 85, 170)    // 中频 (元音 E, I)
      const highFreq = this.getFrequencyRange(frequencyData, 170, 255)  // 高频 (辅音)

      // 根据频率分布确定嘴部形状
      if (lowFreq > midFreq && lowFreq > highFreq) {
        return lowFreq > 0.6 ? 'A' : 'O'  // 大开口或圆形
      } else if (midFreq > lowFreq && midFreq > highFreq) {
        return midFreq > 0.5 ? 'E' : 'I'  // 中等开口或扁形
      } else if (highFreq > 0.4) {
        return 'consonant'  // 辅音形状
      }

      return 'neutral'
    } catch (error) {
      console.error('Frequency analysis error:', error)
      return 'closed'
    }
  }

  /**
   * 获取频率范围的平均值
   */
  private getFrequencyRange(frequencyData: Uint8Array, start: number, end: number): number {
    let sum = 0
    let count = 0

    for (let i = start; i < Math.min(end, frequencyData.length); i++) {
      sum += frequencyData[i]
      count++
    }

    return count > 0 ? (sum / count) / 255 : 0
  }

  /**
   * 基于音量的嘴部形状
   */
  private getVolumeBasedMouthShape(volume: number): string {
    if (volume < 0.1) return 'closed'
    if (volume < 0.3) return 'slightly_open'
    if (volume < 0.6) return 'open'
    return 'wide_open'
  }

  /**
   * 从文本生成嘴部动画
   */
  async animateFromText(text: string, duration: number = 0): Promise<void> {
    if (!this.config.phonemeMapping) {
      // 简单的基于文本长度的动画
      await this.simpleTextAnimation(text, duration)
      return
    }

    // 音素分析和动画
    const phonemes = this.analyzePhonemes(text)
    await this.animatePhonemes(phonemes, duration)
  }

  /**
   * 简单的文本动画
   */
  private async simpleTextAnimation(text: string, duration: number): Promise<void> {
    const words = text.split(' ')
    const wordDuration = duration > 0 ? duration / words.length : 500

    for (const word of words) {
      // 根据单词特征选择嘴部形状
      const mouthShape = this.getWordMouthShape(word)
      this.events.onMouthShapeChanged?.(mouthShape, 0.8)

      await new Promise(resolve => setTimeout(resolve, wordDuration))
    }

    // 动画结束，闭嘴
    this.events.onMouthShapeChanged?('closed', 0)
  }

  /**
   * 根据单词获取嘴部形状
   */
  private getWordMouthShape(word: string): string {
    const vowels = ['a', 'e', 'i', 'o', 'u', 'A', 'E', 'I', 'O', 'U']
    const hasVowel = vowels.some(vowel => word.includes(vowel))
    
    if (hasVowel) {
      // 包含元音，使用开口形状
      if (word.includes('a') || word.includes('A')) return 'A'
      if (word.includes('o') || word.includes('O')) return 'O'
      if (word.includes('e') || word.includes('E')) return 'E'
      if (word.includes('i') || word.includes('I')) return 'I'
      if (word.includes('u') || word.includes('U')) return 'U'
    }

    return 'neutral'
  }

  /**
   * 分析音素
   */
  private analyzePhonemes(text: string): PhonemeMapping[] {
    // 简化的音素分析
    // 在实际应用中，这里会使用更复杂的语音学分析
    const phonemes: PhonemeMapping[] = []
    
    for (let i = 0; i < text.length; i++) {
      const char = text[i].toLowerCase()
      
      if (/[aeiou]/.test(char)) {
        phonemes.push({
          phoneme: char,
          mouthShape: char.toUpperCase(),
          duration: 200
        })
      } else if (/[bcdfghjklmnpqrstvwxyz]/.test(char)) {
        phonemes.push({
          phoneme: char,
          mouthShape: 'consonant',
          duration: 100
        })
      }
    }

    return phonemes
  }

  /**
   * 播放音素动画
   */
  private async animatePhonemes(phonemes: PhonemeMapping[], totalDuration: number): Promise<void> {
    const totalPhonemeDuration = phonemes.reduce((sum, p) => sum + p.duration, 0)
    const timeScale = totalDuration > 0 ? totalDuration / totalPhonemeDuration : 1

    for (const phoneme of phonemes) {
      this.events.onMouthShapeChanged?.(phoneme.mouthShape, 0.8)
      await new Promise(resolve => setTimeout(resolve, phoneme.duration * timeScale))
    }

    this.events.onMouthShapeChanged?('closed', 0)
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<LipSyncConfig>): void {
    this.config = { ...this.config, ...newConfig }
  }

  /**
   * 获取当前状态
   */
  getStatus(): {
    isActive: boolean
    currentMouthShape: string
    speechDetected: boolean
    volume: number
  } {
    return {
      isActive: this.isActive,
      currentMouthShape: this.currentMouthShape,
      speechDetected: this.speechDetected,
      volume: this.getSmoothVolume()
    }
  }

  /**
   * 释放资源
   */
  dispose(): void {
    this.stop()
    
    if (this.audioProcessor) {
      this.audioProcessor.dispose()
      this.audioProcessor = null
    }

    this.volumeHistory = []
  }
}

/**
 * 默认配置
 */
export const DEFAULT_LIP_SYNC_CONFIG: LipSyncConfig = {
  enabled: true,
  sensitivity: 1.0,
  smoothing: 0.3,
  volumeThreshold: 0.1,
  frequencyAnalysis: true,
  phonemeMapping: false
}

/**
 * 创建嘴部动画管理器
 */
export const createLipSyncManager = (
  config: LipSyncConfig = DEFAULT_LIP_SYNC_CONFIG,
  events: LipSyncEvents = {}
) => {
  return new LipSyncManager(config, events)
}
