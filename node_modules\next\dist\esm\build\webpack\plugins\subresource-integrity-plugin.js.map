{"version": 3, "sources": ["../../../../src/build/webpack/plugins/subresource-integrity-plugin.ts"], "names": ["webpack", "sources", "crypto", "SUBRESOURCE_INTEGRITY_MANIFEST", "PLUGIN_NAME", "SubresourceIntegrityPlugin", "constructor", "algorithm", "apply", "compiler", "hooks", "make", "tap", "compilation", "afterOptimizeAssets", "name", "stage", "Compilation", "PROCESS_ASSETS_STAGE_ADDITIONS", "assets", "files", "Set", "asset", "getAssets", "add", "hashes", "file", "values", "Error", "buffer", "hash", "createHash", "update", "digest", "toString", "json", "JSON", "stringify", "RawSource"], "mappings": "AAAA,SAASA,OAAO,EAAEC,OAAO,QAAQ,qCAAoC;AACrE,OAAOC,YAAY,SAAQ;AAC3B,SAASC,8BAA8B,QAAQ,gCAA+B;AAE9E,MAAMC,cAAc;AAIpB,OAAO,MAAMC;IACXC,YAA6BC,UAA0C;yBAA1CA;IAA2C;IAEjEC,MAAMC,QAA0B,EAAE;QACvCA,SAASC,KAAK,CAACC,IAAI,CAACC,GAAG,CAACR,aAAa,CAACS;YACpCA,YAAYH,KAAK,CAACI,mBAAmB,CAACF,GAAG,CACvC;gBACEG,MAAMX;gBACNY,OAAOhB,QAAQiB,WAAW,CAACC,8BAA8B;YAC3D,GACA,CAACC;gBACC,0BAA0B;gBAC1B,IAAIC,QAAQ,IAAIC;gBAChB,KAAK,MAAMC,SAAST,YAAYU,SAAS,GAAI;oBAC3CH,MAAMI,GAAG,CAACF,MAAMP,IAAI;gBACtB;gBAEA,mDAAmD;gBACnD,MAAMU,SAAiC,CAAC;gBACxC,KAAK,MAAMC,QAAQN,MAAMO,MAAM,GAAI;oBACjC,gCAAgC;oBAChC,MAAML,QAAQH,MAAM,CAACO,KAAK;oBAC1B,IAAI,CAACJ,OAAO;wBACV,MAAM,IAAIM,MAAM,CAAC,qBAAqB,EAAEF,KAAK,CAAC;oBAChD;oBAEA,gCAAgC;oBAChC,MAAMG,SAASP,MAAMO,MAAM;oBAE3B,mCAAmC;oBACnC,MAAMC,OAAO5B,OACV6B,UAAU,CAAC,IAAI,CAACxB,SAAS,EACzByB,MAAM,CAACH,QACPI,MAAM,GACNC,QAAQ,CAAC;oBAEZT,MAAM,CAACC,KAAK,GAAG,CAAC,EAAE,IAAI,CAACnB,SAAS,CAAC,CAAC,EAAEuB,KAAK,CAAC;gBAC5C;gBAEA,MAAMK,OAAOC,KAAKC,SAAS,CAACZ,QAAQ,MAAM;gBAC1C,MAAMC,OAAO,YAAYvB;gBACzBgB,MAAM,CAACO,OAAO,MAAM,GAAG,IAAIzB,QAAQqC,SAAS,CAC1C,CAAC,sCAAsC,EAAEF,KAAKC,SAAS,CAACF,MAAM,CAAC;gBAIjEhB,MAAM,CAACO,OAAO,QAAQ,GAAG,IAAIzB,QAAQqC,SAAS,CAC5CH;YAIJ;QAEJ;IACF;AACF"}