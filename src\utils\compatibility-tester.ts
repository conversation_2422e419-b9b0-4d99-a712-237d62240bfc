// 兼容性测试工具
export interface CompatibilityResult {
  feature: string
  supported: boolean
  version?: string
  fallback?: string
  recommendation?: string
}

export interface DeviceInfo {
  userAgent: string
  platform: string
  isMobile: boolean
  isTablet: boolean
  isDesktop: boolean
  browser: {
    name: string
    version: string
  }
  os: {
    name: string
    version: string
  }
  screen: {
    width: number
    height: number
    pixelRatio: number
  }
  hardware: {
    cores: number
    memory: number
  }
}

export class CompatibilityTester {
  private static instance: CompatibilityTester
  private deviceInfo: DeviceInfo | null = null
  private testResults: Map<string, CompatibilityResult> = new Map()

  private constructor() {
    this.detectDevice()
  }

  static getInstance(): CompatibilityTester {
    if (!CompatibilityTester.instance) {
      CompatibilityTester.instance = new CompatibilityTester()
    }
    return CompatibilityTester.instance
  }

  /**
   * 检测设备信息
   */
  private detectDevice(): void {
    const ua = navigator.userAgent
    
    this.deviceInfo = {
      userAgent: ua,
      platform: navigator.platform,
      isMobile: /Mobile|Android|iPhone|iPad/.test(ua),
      isTablet: /iPad|Android.*Tablet/.test(ua),
      isDesktop: !/Mobile|Android|iPhone|iPad/.test(ua),
      browser: this.detectBrowser(ua),
      os: this.detectOS(ua),
      screen: {
        width: screen.width,
        height: screen.height,
        pixelRatio: window.devicePixelRatio || 1
      },
      hardware: {
        cores: navigator.hardwareConcurrency || 4,
        memory: (navigator as any).deviceMemory || 4
      }
    }
  }

  /**
   * 检测浏览器
   */
  private detectBrowser(ua: string): { name: string; version: string } {
    let name = 'Unknown'
    let version = 'Unknown'

    if (ua.includes('Chrome')) {
      name = 'Chrome'
      const match = ua.match(/Chrome\/(\d+)/)
      version = match ? match[1] : 'Unknown'
    } else if (ua.includes('Firefox')) {
      name = 'Firefox'
      const match = ua.match(/Firefox\/(\d+)/)
      version = match ? match[1] : 'Unknown'
    } else if (ua.includes('Safari') && !ua.includes('Chrome')) {
      name = 'Safari'
      const match = ua.match(/Version\/(\d+)/)
      version = match ? match[1] : 'Unknown'
    } else if (ua.includes('Edge')) {
      name = 'Edge'
      const match = ua.match(/Edge\/(\d+)/)
      version = match ? match[1] : 'Unknown'
    }

    return { name, version }
  }

  /**
   * 检测操作系统
   */
  private detectOS(ua: string): { name: string; version: string } {
    let name = 'Unknown'
    let version = 'Unknown'

    if (ua.includes('Windows')) {
      name = 'Windows'
      if (ua.includes('Windows NT 10')) version = '10'
      else if (ua.includes('Windows NT 6.3')) version = '8.1'
      else if (ua.includes('Windows NT 6.2')) version = '8'
      else if (ua.includes('Windows NT 6.1')) version = '7'
    } else if (ua.includes('Mac OS X')) {
      name = 'macOS'
      const match = ua.match(/Mac OS X (\d+_\d+)/)
      version = match ? match[1].replace('_', '.') : 'Unknown'
    } else if (ua.includes('Android')) {
      name = 'Android'
      const match = ua.match(/Android (\d+\.\d+)/)
      version = match ? match[1] : 'Unknown'
    } else if (ua.includes('iPhone') || ua.includes('iPad')) {
      name = 'iOS'
      const match = ua.match(/OS (\d+_\d+)/)
      version = match ? match[1].replace('_', '.') : 'Unknown'
    }

    return { name, version }
  }

  /**
   * 运行完整的兼容性测试
   */
  async runFullCompatibilityTest(): Promise<CompatibilityResult[]> {
    const tests = [
      this.testWebGL(),
      this.testWebRTC(),
      this.testMediaDevices(),
      this.testWebAudio(),
      this.testWebWorkers(),
      this.testIndexedDB(),
      this.testLocalStorage(),
      this.testWebSockets(),
      this.testCanvas(),
      this.testSpeechAPI(),
      this.testFileAPI(),
      this.testPerformanceAPI(),
      this.testIntersectionObserver(),
      this.testResizeObserver(),
      this.testServiceWorker()
    ]

    const results = await Promise.all(tests)
    
    // 存储结果
    results.forEach(result => {
      this.testResults.set(result.feature, result)
    })

    return results
  }

  /**
   * 测试WebGL支持
   */
  private async testWebGL(): Promise<CompatibilityResult> {
    try {
      const canvas = document.createElement('canvas')
      const gl = canvas.getContext('webgl2') || canvas.getContext('webgl')
      
      if (gl) {
        const version = gl.getParameter(gl.VERSION)
        return {
          feature: 'WebGL',
          supported: true,
          version,
          recommendation: 'WebGL支持良好，可以使用3D渲染功能'
        }
      } else {
        return {
          feature: 'WebGL',
          supported: false,
          fallback: '使用2D Canvas渲染',
          recommendation: '建议升级浏览器或显卡驱动'
        }
      }
    } catch (error) {
      return {
        feature: 'WebGL',
        supported: false,
        fallback: '使用2D Canvas渲染',
        recommendation: '浏览器不支持WebGL'
      }
    }
  }

  /**
   * 测试WebRTC支持
   */
  private async testWebRTC(): Promise<CompatibilityResult> {
    try {
      const supported = !!(navigator.mediaDevices && 
                          navigator.mediaDevices.getUserMedia && 
                          window.RTCPeerConnection)
      
      return {
        feature: 'WebRTC',
        supported,
        recommendation: supported ? 
          'WebRTC支持良好，可以使用视频通话功能' : 
          '不支持WebRTC，视频通话功能不可用'
      }
    } catch (error) {
      return {
        feature: 'WebRTC',
        supported: false,
        recommendation: '浏览器不支持WebRTC'
      }
    }
  }

  /**
   * 测试媒体设备支持
   */
  private async testMediaDevices(): Promise<CompatibilityResult> {
    try {
      if (!navigator.mediaDevices) {
        return {
          feature: 'MediaDevices',
          supported: false,
          recommendation: '不支持媒体设备访问'
        }
      }

      const devices = await navigator.mediaDevices.enumerateDevices()
      const hasCamera = devices.some(device => device.kind === 'videoinput')
      const hasMicrophone = devices.some(device => device.kind === 'audioinput')

      return {
        feature: 'MediaDevices',
        supported: true,
        version: `摄像头: ${hasCamera ? '是' : '否'}, 麦克风: ${hasMicrophone ? '是' : '否'}`,
        recommendation: hasCamera && hasMicrophone ? 
          '媒体设备支持完整' : 
          '部分媒体设备不可用'
      }
    } catch (error) {
      return {
        feature: 'MediaDevices',
        supported: false,
        recommendation: '无法访问媒体设备'
      }
    }
  }

  /**
   * 测试Web Audio支持
   */
  private async testWebAudio(): Promise<CompatibilityResult> {
    try {
      const AudioContext = window.AudioContext || (window as any).webkitAudioContext
      const supported = !!AudioContext
      
      return {
        feature: 'WebAudio',
        supported,
        recommendation: supported ? 
          'Web Audio支持良好，可以使用音频处理功能' : 
          '不支持Web Audio，音频功能受限'
      }
    } catch (error) {
      return {
        feature: 'WebAudio',
        supported: false,
        recommendation: '浏览器不支持Web Audio'
      }
    }
  }

  /**
   * 测试Web Workers支持
   */
  private async testWebWorkers(): Promise<CompatibilityResult> {
    try {
      const supported = !!window.Worker
      
      return {
        feature: 'WebWorkers',
        supported,
        recommendation: supported ? 
          'Web Workers支持良好，可以使用多线程处理' : 
          '不支持Web Workers，性能可能受影响'
      }
    } catch (error) {
      return {
        feature: 'WebWorkers',
        supported: false,
        recommendation: '浏览器不支持Web Workers'
      }
    }
  }

  /**
   * 测试IndexedDB支持
   */
  private async testIndexedDB(): Promise<CompatibilityResult> {
    try {
      const supported = !!window.indexedDB
      
      return {
        feature: 'IndexedDB',
        supported,
        fallback: supported ? undefined : 'localStorage',
        recommendation: supported ? 
          'IndexedDB支持良好，可以使用本地数据库' : 
          '不支持IndexedDB，使用localStorage作为替代'
      }
    } catch (error) {
      return {
        feature: 'IndexedDB',
        supported: false,
        fallback: 'localStorage',
        recommendation: '浏览器不支持IndexedDB'
      }
    }
  }

  /**
   * 测试LocalStorage支持
   */
  private async testLocalStorage(): Promise<CompatibilityResult> {
    try {
      const testKey = '__test__'
      localStorage.setItem(testKey, 'test')
      localStorage.removeItem(testKey)
      
      return {
        feature: 'LocalStorage',
        supported: true,
        recommendation: 'LocalStorage支持良好'
      }
    } catch (error) {
      return {
        feature: 'LocalStorage',
        supported: false,
        recommendation: '不支持LocalStorage，无法保存本地数据'
      }
    }
  }

  /**
   * 测试WebSockets支持
   */
  private async testWebSockets(): Promise<CompatibilityResult> {
    try {
      const supported = !!window.WebSocket
      
      return {
        feature: 'WebSockets',
        supported,
        fallback: supported ? undefined : 'HTTP轮询',
        recommendation: supported ? 
          'WebSockets支持良好，可以使用实时通信' : 
          '不支持WebSockets，使用HTTP轮询作为替代'
      }
    } catch (error) {
      return {
        feature: 'WebSockets',
        supported: false,
        fallback: 'HTTP轮询',
        recommendation: '浏览器不支持WebSockets'
      }
    }
  }

  /**
   * 测试Canvas支持
   */
  private async testCanvas(): Promise<CompatibilityResult> {
    try {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      
      return {
        feature: 'Canvas',
        supported: !!ctx,
        recommendation: ctx ? 
          'Canvas支持良好，可以使用2D渲染' : 
          '不支持Canvas，图形功能不可用'
      }
    } catch (error) {
      return {
        feature: 'Canvas',
        supported: false,
        recommendation: '浏览器不支持Canvas'
      }
    }
  }

  /**
   * 测试语音API支持
   */
  private async testSpeechAPI(): Promise<CompatibilityResult> {
    try {
      const speechSynthesis = !!window.speechSynthesis
      const speechRecognition = !!(window.SpeechRecognition || (window as any).webkitSpeechRecognition)
      
      return {
        feature: 'SpeechAPI',
        supported: speechSynthesis || speechRecognition,
        version: `TTS: ${speechSynthesis ? '是' : '否'}, STT: ${speechRecognition ? '是' : '否'}`,
        recommendation: speechSynthesis && speechRecognition ? 
          '语音API支持完整' : 
          '部分语音功能不可用'
      }
    } catch (error) {
      return {
        feature: 'SpeechAPI',
        supported: false,
        recommendation: '浏览器不支持语音API'
      }
    }
  }

  /**
   * 测试File API支持
   */
  private async testFileAPI(): Promise<CompatibilityResult> {
    try {
      const supported = !!(window.File && window.FileReader && window.FileList && window.Blob)
      
      return {
        feature: 'FileAPI',
        supported,
        recommendation: supported ? 
          'File API支持良好，可以处理文件上传' : 
          '不支持File API，文件功能受限'
      }
    } catch (error) {
      return {
        feature: 'FileAPI',
        supported: false,
        recommendation: '浏览器不支持File API'
      }
    }
  }

  /**
   * 测试Performance API支持
   */
  private async testPerformanceAPI(): Promise<CompatibilityResult> {
    try {
      const supported = !!window.performance
      
      return {
        feature: 'PerformanceAPI',
        supported,
        recommendation: supported ? 
          'Performance API支持良好，可以进行性能监控' : 
          '不支持Performance API，无法进行性能监控'
      }
    } catch (error) {
      return {
        feature: 'PerformanceAPI',
        supported: false,
        recommendation: '浏览器不支持Performance API'
      }
    }
  }

  /**
   * 测试Intersection Observer支持
   */
  private async testIntersectionObserver(): Promise<CompatibilityResult> {
    try {
      const supported = !!window.IntersectionObserver
      
      return {
        feature: 'IntersectionObserver',
        supported,
        fallback: supported ? undefined : 'scroll事件',
        recommendation: supported ? 
          'Intersection Observer支持良好' : 
          '不支持Intersection Observer，使用scroll事件作为替代'
      }
    } catch (error) {
      return {
        feature: 'IntersectionObserver',
        supported: false,
        fallback: 'scroll事件',
        recommendation: '浏览器不支持Intersection Observer'
      }
    }
  }

  /**
   * 测试Resize Observer支持
   */
  private async testResizeObserver(): Promise<CompatibilityResult> {
    try {
      const supported = !!window.ResizeObserver
      
      return {
        feature: 'ResizeObserver',
        supported,
        fallback: supported ? undefined : 'resize事件',
        recommendation: supported ? 
          'Resize Observer支持良好' : 
          '不支持Resize Observer，使用resize事件作为替代'
      }
    } catch (error) {
      return {
        feature: 'ResizeObserver',
        supported: false,
        fallback: 'resize事件',
        recommendation: '浏览器不支持Resize Observer'
      }
    }
  }

  /**
   * 测试Service Worker支持
   */
  private async testServiceWorker(): Promise<CompatibilityResult> {
    try {
      const supported = !!navigator.serviceWorker
      
      return {
        feature: 'ServiceWorker',
        supported,
        recommendation: supported ? 
          'Service Worker支持良好，可以使用离线功能' : 
          '不支持Service Worker，无法使用离线功能'
      }
    } catch (error) {
      return {
        feature: 'ServiceWorker',
        supported: false,
        recommendation: '浏览器不支持Service Worker'
      }
    }
  }

  /**
   * 获取设备信息
   */
  getDeviceInfo(): DeviceInfo | null {
    return this.deviceInfo
  }

  /**
   * 获取测试结果
   */
  getTestResults(): CompatibilityResult[] {
    return Array.from(this.testResults.values())
  }

  /**
   * 获取兼容性评分
   */
  getCompatibilityScore(): number {
    const results = this.getTestResults()
    if (results.length === 0) return 0

    const supportedCount = results.filter(r => r.supported).length
    return Math.round((supportedCount / results.length) * 100)
  }

  /**
   * 获取推荐配置
   */
  getRecommendedSettings(): { [key: string]: any } {
    const deviceInfo = this.getDeviceInfo()
    const score = this.getCompatibilityScore()

    if (!deviceInfo) return {}

    const settings: { [key: string]: any } = {}

    // 根据设备性能调整设置
    if (deviceInfo.isMobile) {
      settings.renderQuality = 'medium'
      settings.maxFPS = 30
      settings.enableShadows = false
    } else if (score < 70) {
      settings.renderQuality = 'low'
      settings.maxFPS = 30
      settings.enableShadows = false
    } else {
      settings.renderQuality = 'high'
      settings.maxFPS = 60
      settings.enableShadows = true
    }

    // 根据内存调整设置
    if (deviceInfo.hardware.memory < 4) {
      settings.textureQuality = 'low'
      settings.maxConcurrentAnimations = 3
    } else {
      settings.textureQuality = 'high'
      settings.maxConcurrentAnimations = 10
    }

    return settings
  }
}

// 导出单例实例
export const compatibilityTester = CompatibilityTester.getInstance()
