{"version": 3, "sources": ["../../../../src/build/webpack/plugins/profiling-plugin.ts"], "names": ["NormalModule", "pluginName", "spans", "WeakMap", "moduleSpansByCompilation", "makeSpanByCompilation", "sealSpanByCompilation", "webpackInvalidSpans", "TRACE_LABELS_SEAL", "inTraceLabelsSeal", "label", "some", "l", "startsWith", "Profiling<PERSON><PERSON><PERSON>", "constructor", "runWebpackSpan", "apply", "compiler", "traceTopLevelHooks", "traceCompilationHooks", "traceHookPair", "spanName", "startHook", "stopHook", "parentSpan", "attrs", "onStart", "onStop", "span", "tap", "name", "stage", "Infinity", "params", "attributes", "<PERSON><PERSON><PERSON><PERSON>", "stop", "hooks", "compilation", "afterCompile", "get", "set", "options", "mode", "invalid", "done", "delete", "fileName", "trigger", "emit", "afterEmit", "make", "finishMake", "compilationSpan", "_span", "buildModule", "module", "moduleType", "userRequest", "split", "pop", "issuerModule", "moduleGraph", "get<PERSON><PERSON><PERSON>", "moduleSpans", "issuerSpan", "incomingConnection", "getIncomingConnections", "entrySpan", "dependency", "setAttribute", "layer", "moduleHooks", "getCompilationHooks", "readResource", "for", "undefined", "intercept", "register", "tapInfo", "fn", "loaderContext", "callback", "moduleSpan", "currentTraceSpan", "err", "result", "loader", "succeedModule", "failedModule", "seal", "afterSeal", "addEntry", "entry", "addEntrySpan", "request", "<PERSON><PERSON><PERSON><PERSON>", "failedEntry", "beforeChunks", "after<PERSON><PERSON><PERSON>", "optimize", "reviveModules", "optimizeModules", "afterOptimizeModules", "optimizeChunks", "afterOptimizeChunks", "optimizeTree", "afterOptimizeTree", "optimizeChunkModules", "afterOptimizeChunkModules", "beforeModuleHash", "afterModuleHash", "beforeCodeGeneration", "afterCodeGeneration", "beforeHash", "afterHash", "beforeModuleAssets", "logs", "Map", "originalTime", "logger", "time", "originalTimeEnd", "timeEnd", "call", "replace"], "mappings": "AAAA,SAASA,YAAY,QAAQ,qCAAoC;AAIjE,MAAMC,aAAa;AACnB,OAAO,MAAMC,QAAQ,IAAIC,UAAuD;AAChF,MAAMC,2BAA2B,IAAID;AAIrC,MAAME,wBAAwB,IAAIF;AAClC,MAAMG,wBAAwB,IAAIH;AAClC,OAAO,MAAMI,sBAAsB,IAAIJ,UAAoB;AAE3D,MAAMK,oBAAoB;IACxB;IACA;IACA;IACA;IACA;CACD;AAED,SAASC,kBAAkBC,KAAa;IACtC,OAAOF,kBAAkBG,IAAI,CAAC,CAACC,IAAMF,MAAMG,UAAU,CAACD;AACxD;AAEA,OAAO,MAAME;IAIXC,YAAY,EAAEC,cAAc,EAA4B,CAAE;QACxD,IAAI,CAACA,cAAc,GAAGA;IACxB;IACAC,MAAMC,QAAa,EAAE;QACnB,IAAI,CAACC,kBAAkB,CAACD;QACxB,IAAI,CAACE,qBAAqB,CAACF;QAC3B,IAAI,CAACA,QAAQ,GAAGA;IAClB;IAEAG,cACEC,QAAiC,EACjCC,SAAc,EACdC,QAAa,EACb,EACEC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,MAAM,EAMP,GAAG,CAAC,CAAC,EACN;QACA,IAAIC;QACJN,UAAUO,GAAG,CACX;YAAEC,MAAM9B;YAAY+B,OAAO,CAACC;QAAS,GACrC,CAAC,GAAGC;YACF,MAAMH,OAAO,OAAOT,aAAa,aAAaA,aAAaA;YAC3D,MAAMa,aAAaT,QAAQA,SAASQ,UAAUR;YAC9CG,OAAOJ,aACHA,cAAcS,QAAQE,UAAU,CAACL,MAAMI,cACvC,IAAI,CAACnB,cAAc,CAACoB,UAAU,CAACL,MAAMI;YAEzC,IAAIR,SAASA,QAAQE,SAASK;QAChC;QAEFV,SAASM,GAAG,CAAC;YAAEC,MAAM9B;YAAY+B,OAAOC;QAAS,GAAG,CAAC,GAAGC;YACtD,gEAAgE;YAChE,6DAA6D;YAC7D,0BAA0B;YAC1B,IAAI,CAACL,MAAM;gBACT;YACF;YAEA,IAAID,QAAQA,OAAOC,SAASK;YAC5BL,KAAKQ,IAAI;QACX;IACF;IAEAlB,mBAAmBD,QAAa,EAAE;QAChC,IAAI,CAACG,aAAa,CAChB,uBACAH,SAASoB,KAAK,CAACC,WAAW,EAC1BrB,SAASoB,KAAK,CAACE,YAAY,EAC3B;YACEf,YAAY,IACVlB,oBAAoBkC,GAAG,CAACvB,aAAa,IAAI,CAACF,cAAc;YAC1DU,OAAO,IAAO,CAAA;oBAAEK,MAAMb,SAASa,IAAI;gBAAC,CAAA;YACpCJ,SAAS,CAACE,MAAMU;gBACdrC,MAAMwC,GAAG,CAACH,aAAaV;gBACvB3B,MAAMwC,GAAG,CAACxB,UAAUW;gBACpBzB,yBAAyBsC,GAAG,CAACH,aAAa,IAAIpC;YAChD;QACF;QAGF,IAAIe,SAASyB,OAAO,CAACC,IAAI,KAAK,eAAe;YAC3C,IAAI,CAACvB,aAAa,CAChB,IAAM,CAAC,oBAAoB,EAAEH,SAASa,IAAI,CAAC,CAAC,EAC5Cb,SAASoB,KAAK,CAACO,OAAO,EACtB3B,SAASoB,KAAK,CAACQ,IAAI,EACnB;gBACEnB,SAAS,CAACE,OAAStB,oBAAoBmC,GAAG,CAACxB,UAAUW;gBACrDD,QAAQ,IAAMrB,oBAAoBwC,MAAM,CAAC7B;gBACzCQ,OAAO,CAACsB,WAAmB,CAAA;wBACzBC,SAASD,YAAY;oBACvB,CAAA;YACF;QAEJ;IACF;IAEA5B,sBAAsBF,QAAa,EAAE;QACnC,IAAI,CAACG,aAAa,CAAC,QAAQH,SAASoB,KAAK,CAACY,IAAI,EAAEhC,SAASoB,KAAK,CAACa,SAAS,EAAE;YACxE1B,YAAY,IACVlB,oBAAoBkC,GAAG,CAACvB,aAAa,IAAI,CAACF,cAAc;QAC5D;QAEA,IAAI,CAACK,aAAa,CAAC,QAAQH,SAASoB,KAAK,CAACc,IAAI,EAAElC,SAASoB,KAAK,CAACe,UAAU,EAAE;YACzE5B,YAAY,CAACc;gBACX,MAAMe,kBAAkBpD,MAAMuC,GAAG,CAACF;gBAClC,IAAI,CAACe,iBAAiB;oBACpB,OAAO/C,oBAAoBkC,GAAG,CAACvB,aAAa,IAAI,CAACF,cAAc;gBACjE;gBAEA,OAAOsC;YACT;YACA3B,SAAS,CAACE,MAAMU;gBACdlC,sBAAsBqC,GAAG,CAACH,aAAaV;YACzC;YACAD,QAAQ,CAAC2B,OAAOhB;gBACdlC,sBAAsB0C,MAAM,CAACR;YAC/B;QACF;QAEArB,SAASoB,KAAK,CAACC,WAAW,CAACT,GAAG,CAC5B;YAAEC,MAAM9B;YAAY+B,OAAO,CAACC;QAAS,GACrC,CAACM;YACCA,YAAYD,KAAK,CAACkB,WAAW,CAAC1B,GAAG,CAAC7B,YAAY,CAACwD;oBASxBlB;gBARrB,MAAMmB,aAAa,AAAC,CAAA;oBAClB,IAAI,CAACD,OAAOE,WAAW,EAAE;wBACvB,OAAO;oBACT;oBAEA,OAAOF,OAAOE,WAAW,CAACC,KAAK,CAAC,KAAKC,GAAG;gBAC1C,CAAA;gBAEA,MAAMC,eAAevB,gCAAAA,2BAAAA,YAAawB,WAAW,qBAAxBxB,yBAA0ByB,SAAS,CAACP;gBAEzD,IAAI5B;gBAEJ,MAAMoC,cAAc7D,yBAAyBqC,GAAG,CAACF;gBACjD,MAAMjB,WAAW,CAAC,YAAY,EAAEoC,aAAa,CAAC,CAAC,EAAEA,WAAW,CAAC,GAAG,GAAG,CAAC;gBACpE,MAAMQ,aACJJ,iBAAgBG,+BAAAA,YAAaxB,GAAG,CAACqB;gBACnC,IAAII,YAAY;oBACdrC,OAAOqC,WAAW9B,UAAU,CAACd;gBAC/B,OAAO;oBACL,IAAIG;oBACJ,KAAK,MAAM0C,sBAAsB5B,YAAYwB,WAAW,CAACK,sBAAsB,CAC7EX,QACC;wBACD,MAAMY,YAAYnE,MAAMuC,GAAG,CAAC0B,mBAAmBG,UAAU;wBACzD,IAAID,WAAW;4BACb5C,aAAa4C;4BACb;wBACF;oBACF;oBAEA,IAAI,CAAC5C,YAAY;wBACf,MAAM6B,kBAAkBpD,MAAMuC,GAAG,CAACF;wBAClC,IAAI,CAACe,iBAAiB;4BACpB;wBACF;wBAEA7B,aAAa6B;oBACf;oBACAzB,OAAOJ,WAAWW,UAAU,CAACd;gBAC/B;gBACAO,KAAK0C,YAAY,CAAC,QAAQd,OAAOE,WAAW;gBAC5C9B,KAAK0C,YAAY,CAAC,SAASd,OAAOe,KAAK;gBACvCP,YAAavB,GAAG,CAACe,QAAQ5B;YAC3B;YAEA,MAAM4C,cAAczE,aAAa0E,mBAAmB,CAACnC;YACrDkC,YAAYE,YAAY,CAACC,GAAG,CAACC,WAAWC,SAAS,CAAC;gBAChDC,UAASC,OAAY;oBACnB,MAAMC,KAAKD,QAAQC,EAAE;oBACrBD,QAAQC,EAAE,GAAG,CAACC,eAAoBC;wBAChC,MAAMC,aACJF,cAAcG,gBAAgB,CAACjD,UAAU,CAAC,CAAC,aAAa,CAAC;wBAC3D6C,GAAGC,eAAe,CAACI,KAAUC;4BAC3BH,WAAW/C,IAAI;4BACf8C,SAASG,KAAKC;wBAChB;oBACF;oBACA,OAAOP;gBACT;YACF;YAEAP,YAAYe,MAAM,CAAC1D,GAAG,CACpB7B,YACA,CAACiF,eAAoBzB;oBACArD;gBAAnB,MAAMgF,cAAahF,gCAAAA,yBAChBqC,GAAG,CAACF,iCADYnC,8BAEfqC,GAAG,CAACgB;gBACRyB,cAAcG,gBAAgB,GAAGD;YACnC;YAGF7C,YAAYD,KAAK,CAACmD,aAAa,CAAC3D,GAAG,CAAC7B,YAAY,CAACwD;oBAC/CrD,mCAAAA;gBAAAA,6CAAAA,gCAAAA,yBAA0BqC,GAAG,CAACF,kCAA9BnC,oCAAAA,8BAA4CqC,GAAG,CAACgB,4BAAhDrD,kCAAyDiC,IAAI;YAC/D;YACAE,YAAYD,KAAK,CAACoD,YAAY,CAAC5D,GAAG,CAAC7B,YAAY,CAACwD;oBAC9CrD,mCAAAA;gBAAAA,6CAAAA,gCAAAA,yBAA0BqC,GAAG,CAACF,kCAA9BnC,oCAAAA,8BAA4CqC,GAAG,CAACgB,4BAAhDrD,kCAAyDiC,IAAI;YAC/D;YAEA,IAAI,CAAChB,aAAa,CAChB,QACAkB,YAAYD,KAAK,CAACqD,IAAI,EACtBpD,YAAYD,KAAK,CAACsD,SAAS,EAC3B;gBACEnE,YAAY,IAAMvB,MAAMuC,GAAG,CAACF;gBAC5BZ,SAAQE,IAAI;oBACVvB,sBAAsBoC,GAAG,CAACH,aAAaV;gBACzC;gBACAD;oBACEtB,sBAAsByC,MAAM,CAACR;gBAC/B;YACF;YAGFA,YAAYD,KAAK,CAACuD,QAAQ,CAAC/D,GAAG,CAAC7B,YAAY,CAAC6F;gBAC1C,MAAMrE,aACJpB,sBAAsBoC,GAAG,CAACF,gBAAgBrC,MAAMuC,GAAG,CAACF;gBACtD,IAAI,CAACd,YAAY;oBACf;gBACF;gBACA,MAAMsE,eAAetE,WAAWW,UAAU,CAAC;gBAC3C2D,aAAaxB,YAAY,CAAC,WAAWuB,MAAME,OAAO;gBAClD9F,MAAMwC,GAAG,CAACoD,OAAOC;YACnB;YAEAxD,YAAYD,KAAK,CAAC2D,YAAY,CAACnE,GAAG,CAAC7B,YAAY,CAAC6F;oBAC9C5F;iBAAAA,aAAAA,MAAMuC,GAAG,CAACqD,2BAAV5F,WAAkBmC,IAAI;gBACtBnC,MAAM6C,MAAM,CAAC+C;YACf;YACAvD,YAAYD,KAAK,CAAC4D,WAAW,CAACpE,GAAG,CAAC7B,YAAY,CAAC6F;oBAC7C5F;iBAAAA,aAAAA,MAAMuC,GAAG,CAACqD,2BAAV5F,WAAkBmC,IAAI;gBACtBnC,MAAM6C,MAAM,CAAC+C;YACf;YAEA,IAAI,CAACzE,aAAa,CAChB,eACAkB,YAAYD,KAAK,CAAC6D,YAAY,EAC9B5D,YAAYD,KAAK,CAAC8D,WAAW,EAC7B;gBACE3E,YAAY,IACVnB,sBAAsBmC,GAAG,CAACF,gBAAgBrC,MAAMuC,GAAG,CAACF;YACxD;YAEF,IAAI,CAAClB,aAAa,CAChB,YACAkB,YAAYD,KAAK,CAAC+D,QAAQ,EAC1B9D,YAAYD,KAAK,CAACgE,aAAa,EAC/B;gBACE7E,YAAY,IACVnB,sBAAsBmC,GAAG,CAACF,gBAAgBrC,MAAMuC,GAAG,CAACF;YACxD;YAEF,IAAI,CAAClB,aAAa,CAChB,oBACAkB,YAAYD,KAAK,CAACiE,eAAe,EACjChE,YAAYD,KAAK,CAACkE,oBAAoB,EACtC;gBACE/E,YAAY,IACVnB,sBAAsBmC,GAAG,CAACF,gBAAgBrC,MAAMuC,GAAG,CAACF;YACxD;YAEF,IAAI,CAAClB,aAAa,CAChB,mBACAkB,YAAYD,KAAK,CAACmE,cAAc,EAChClE,YAAYD,KAAK,CAACoE,mBAAmB,EACrC;gBACEjF,YAAY,IACVnB,sBAAsBmC,GAAG,CAACF,gBAAgBrC,MAAMuC,GAAG,CAACF;YACxD;YAEF,IAAI,CAAClB,aAAa,CAChB,iBACAkB,YAAYD,KAAK,CAACqE,YAAY,EAC9BpE,YAAYD,KAAK,CAACsE,iBAAiB,EACnC;gBACEnF,YAAY,IACVnB,sBAAsBmC,GAAG,CAACF,gBAAgBrC,MAAMuC,GAAG,CAACF;YACxD;YAEF,IAAI,CAAClB,aAAa,CAChB,0BACAkB,YAAYD,KAAK,CAACuE,oBAAoB,EACtCtE,YAAYD,KAAK,CAACwE,yBAAyB,EAC3C;gBACErF,YAAY,IACVnB,sBAAsBmC,GAAG,CAACF,gBAAgBrC,MAAMuC,GAAG,CAACF;YACxD;YAEF,IAAI,CAAClB,aAAa,CAChB,eACAkB,YAAYD,KAAK,CAACyE,gBAAgB,EAClCxE,YAAYD,KAAK,CAAC0E,eAAe,EACjC;gBACEvF,YAAY,IACVnB,sBAAsBmC,GAAG,CAACF,gBAAgBrC,MAAMuC,GAAG,CAACF;YACxD;YAEF,IAAI,CAAClB,aAAa,CAChB,mBACAkB,YAAYD,KAAK,CAAC2E,oBAAoB,EACtC1E,YAAYD,KAAK,CAAC4E,mBAAmB,EACrC;gBACEzF,YAAY,IACVnB,sBAAsBmC,GAAG,CAACF,gBAAgBrC,MAAMuC,GAAG,CAACF;YACxD;YAEF,IAAI,CAAClB,aAAa,CAChB,QACAkB,YAAYD,KAAK,CAAC6E,UAAU,EAC5B5E,YAAYD,KAAK,CAAC8E,SAAS,EAC3B;gBACE3F,YAAY,IACVnB,sBAAsBmC,GAAG,CAACF,gBAAgBrC,MAAMuC,GAAG,CAACF;YACxD;YAEF,IAAI,CAAClB,aAAa,CAChB,wBACAkB,YAAYD,KAAK,CAAC8E,SAAS,EAC3B7E,YAAYD,KAAK,CAAC+E,kBAAkB,EACpC;gBACE5F,YAAY,IACVnB,sBAAsBmC,GAAG,CAACF,gBAAgBrC,MAAMuC,GAAG,CAACF;YACxD;YAGF,MAAM+E,OAAO,IAAIC;YACjB,MAAMC,eAAejF,YAAYkF,MAAM,CAACC,IAAI;YAC5C,MAAMC,kBAAkBpF,YAAYkF,MAAM,CAACG,OAAO;YAElDrF,YAAYkF,MAAM,CAACC,IAAI,GAAG,CAAChH;gBACzB,IAAI,CAACD,kBAAkBC,QAAQ;oBAC7B,OAAO8G,aAAaK,IAAI,CAACtF,YAAYkF,MAAM,EAAE/G;gBAC/C;gBACA,MAAMmB,OAAOvB,sBAAsBmC,GAAG,CAACF;gBACvC,IAAIV,MAAM;oBACRyF,KAAK5E,GAAG,CAAChC,OAAOmB,KAAKO,UAAU,CAAC1B,MAAMoH,OAAO,CAAC,MAAM;gBACtD;gBACA,OAAON,aAAaK,IAAI,CAACtF,YAAYkF,MAAM,EAAE/G;YAC/C;YACA6B,YAAYkF,MAAM,CAACG,OAAO,GAAG,CAAClH;gBAC5B,IAAI,CAACD,kBAAkBC,QAAQ;oBAC7B,OAAOiH,gBAAgBE,IAAI,CAACtF,YAAYkF,MAAM,EAAE/G;gBAClD;gBAEA,MAAMmB,OAAOyF,KAAK7E,GAAG,CAAC/B;gBACtB,IAAImB,MAAM;oBACRA,KAAKQ,IAAI;oBACTiF,KAAKvE,MAAM,CAACrC;gBACd;gBACA,OAAOiH,gBAAgBE,IAAI,CAACtF,YAAYkF,MAAM,EAAE/G;YAClD;QACF;IAEJ;AACF"}