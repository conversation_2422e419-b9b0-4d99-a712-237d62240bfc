{"version": 3, "sources": ["../../../../src/server/lib/trace/tracer.ts"], "names": ["NextVanillaSpanAllowlist", "api", "process", "env", "NEXT_RUNTIME", "require", "err", "context", "propagation", "trace", "SpanStatusCode", "SpanKind", "ROOT_CONTEXT", "isPromise", "p", "then", "closeSpanWithError", "span", "error", "bubble", "setAttribute", "recordException", "setStatus", "code", "ERROR", "message", "end", "rootSpanAttributesStore", "Map", "rootSpanIdKey", "createContextKey", "lastSpanId", "getSpanId", "NextTracerImpl", "getTracerInstance", "getTracer", "getContext", "getActiveScopeSpan", "getSpan", "active", "withPropagatedContext", "carrier", "fn", "getter", "activeContext", "getSpanContext", "remoteContext", "extract", "with", "args", "type", "fnOrOptions", "fnOrEmpty", "options", "includes", "NEXT_OTEL_VERBOSE", "hideSpan", "spanName", "spanContext", "parentSpan", "isRootSpan", "isRemote", "spanId", "attributes", "setValue", "startActiveSpan", "onCleanup", "delete", "set", "Object", "entries", "length", "result", "finally", "wrap", "tracer", "name", "optionsObj", "apply", "arguments", "lastArgId", "cb", "scopeBoundCb", "bind", "_span", "done", "startSpan", "setSpan", "undefined", "getRootSpanAttributes", "getValue", "get"], "mappings": "AACA,SAASA,wBAAwB,QAAQ,cAAa;AAWtD,IAAIC;AAEJ,gFAAgF;AAChF,8EAA8E;AAC9E,uCAAuC;AACvC,0EAA0E;AAC1E,+EAA+E;AAC/E,4CAA4C;AAC5C,6CAA6C;AAC7C,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;IACvCH,MAAMI,QAAQ;AAChB,OAAO;IACL,IAAI;QACFJ,MAAMI,QAAQ;IAChB,EAAE,OAAOC,KAAK;QACZL,MAAMI,QAAQ;IAChB;AACF;AAEA,MAAM,EAAEE,OAAO,EAAEC,WAAW,EAAEC,KAAK,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,YAAY,EAAE,GAC3EX;AAEF,MAAMY,YAAY,CAAIC;IACpB,OAAOA,MAAM,QAAQ,OAAOA,MAAM,YAAY,OAAOA,EAAEC,IAAI,KAAK;AAClE;AAIA,MAAMC,qBAAqB,CAACC,MAAYC;IACtC,IAAI,CAACA,yBAAD,AAACA,MAAoCC,MAAM,MAAK,MAAM;QACxDF,KAAKG,YAAY,CAAC,eAAe;IACnC,OAAO;QACL,IAAIF,OAAO;YACTD,KAAKI,eAAe,CAACH;QACvB;QACAD,KAAKK,SAAS,CAAC;YAAEC,MAAMb,eAAec,KAAK;YAAEC,OAAO,EAAEP,yBAAAA,MAAOO,OAAO;QAAC;IACvE;IACAR,KAAKS,GAAG;AACV;AAkGA,8EAA8E,GAC9E,MAAMC,0BAA0B,IAAIC;AAIpC,MAAMC,gBAAgB5B,IAAI6B,gBAAgB,CAAC;AAC3C,IAAIC,aAAa;AACjB,MAAMC,YAAY,IAAMD;AAExB,MAAME;IACJ;;;;GAIC,GACD,AAAQC,oBAA4B;QAClC,OAAOzB,MAAM0B,SAAS,CAAC,WAAW;IACpC;IAEOC,aAAyB;QAC9B,OAAO7B;IACT;IAEO8B,qBAAuC;QAC5C,OAAO5B,MAAM6B,OAAO,CAAC/B,2BAAAA,QAASgC,MAAM;IACtC;IAEOC,sBACLC,OAAU,EACVC,EAAW,EACXC,MAAyB,EACtB;QACH,MAAMC,gBAAgBrC,QAAQgC,MAAM;QACpC,IAAI9B,MAAMoC,cAAc,CAACD,gBAAgB;YACvC,qDAAqD;YACrD,OAAOF;QACT;QACA,MAAMI,gBAAgBtC,YAAYuC,OAAO,CAACH,eAAeH,SAASE;QAClE,OAAOpC,QAAQyC,IAAI,CAACF,eAAeJ;IACrC;IAsBOjC,MAAS,GAAGwC,IAAgB,EAAE;YAwCxBxC;QAvCX,MAAM,CAACyC,MAAMC,aAAaC,UAAU,GAAGH;QAEvC,+BAA+B;QAC/B,MAAM,EACJP,EAAE,EACFW,OAAO,EACR,GAIC,OAAOF,gBAAgB,aACnB;YACET,IAAIS;YACJE,SAAS,CAAC;QACZ,IACA;YACEX,IAAIU;YACJC,SAAS;gBAAE,GAAGF,WAAW;YAAC;QAC5B;QAEN,IACE,AAAC,CAACnD,yBAAyBsD,QAAQ,CAACJ,SAClChD,QAAQC,GAAG,CAACoD,iBAAiB,KAAK,OACpCF,QAAQG,QAAQ,EAChB;YACA,OAAOd;QACT;QAEA,MAAMe,WAAWJ,QAAQI,QAAQ,IAAIP;QAErC,mHAAmH;QACnH,IAAIQ,cAAc,IAAI,CAACb,cAAc,CACnCQ,CAAAA,2BAAAA,QAASM,UAAU,KAAI,IAAI,CAACtB,kBAAkB;QAEhD,IAAIuB,aAAa;QAEjB,IAAI,CAACF,aAAa;YAChBA,cAAc9C;YACdgD,aAAa;QACf,OAAO,KAAInD,wBAAAA,MAAMoC,cAAc,CAACa,iCAArBjD,sBAAmCoD,QAAQ,EAAE;YACtDD,aAAa;QACf;QAEA,MAAME,SAAS9B;QAEfqB,QAAQU,UAAU,GAAG;YACnB,kBAAkBN;YAClB,kBAAkBP;YAClB,GAAGG,QAAQU,UAAU;QACvB;QAEA,OAAOxD,QAAQyC,IAAI,CAACU,YAAYM,QAAQ,CAACnC,eAAeiC,SAAS,IAC/D,IAAI,CAAC5B,iBAAiB,GAAG+B,eAAe,CACtCR,UACAJ,SACA,CAACpC;gBACC,MAAMiD,YAAY;oBAChBvC,wBAAwBwC,MAAM,CAACL;gBACjC;gBACA,IAAIF,YAAY;oBACdjC,wBAAwByC,GAAG,CACzBN,QACA,IAAIlC,IACFyC,OAAOC,OAAO,CAACjB,QAAQU,UAAU,IAAI,CAAC;gBAM5C;gBACA,IAAI;oBACF,IAAIrB,GAAG6B,MAAM,GAAG,GAAG;wBACjB,OAAO7B,GAAGzB,MAAM,CAACX,MAAgBU,mBAAmBC,MAAMX;oBAC5D;oBAEA,MAAMkE,SAAS9B,GAAGzB;oBAElB,IAAIJ,UAAU2D,SAAS;wBACrBA,OACGzD,IAAI,CACH,IAAME,KAAKS,GAAG,IACd,CAACpB,MAAQU,mBAAmBC,MAAMX,MAEnCmE,OAAO,CAACP;oBACb,OAAO;wBACLjD,KAAKS,GAAG;wBACRwC;oBACF;oBAEA,OAAOM;gBACT,EAAE,OAAOlE,KAAU;oBACjBU,mBAAmBC,MAAMX;oBACzB4D;oBACA,MAAM5D;gBACR;YACF;IAGN;IAaOoE,KAAK,GAAGzB,IAAgB,EAAE;QAC/B,MAAM0B,SAAS,IAAI;QACnB,MAAM,CAACC,MAAMvB,SAASX,GAAG,GACvBO,KAAKsB,MAAM,KAAK,IAAItB,OAAO;YAACA,IAAI,CAAC,EAAE;YAAE,CAAC;YAAGA,IAAI,CAAC,EAAE;SAAC;QAEnD,IACE,CAACjD,yBAAyBsD,QAAQ,CAACsB,SACnC1E,QAAQC,GAAG,CAACoD,iBAAiB,KAAK,KAClC;YACA,OAAOb;QACT;QAEA,OAAO;YACL,IAAImC,aAAaxB;YACjB,IAAI,OAAOwB,eAAe,cAAc,OAAOnC,OAAO,YAAY;gBAChEmC,aAAaA,WAAWC,KAAK,CAAC,IAAI,EAAEC;YACtC;YAEA,MAAMC,YAAYD,UAAUR,MAAM,GAAG;YACrC,MAAMU,KAAKF,SAAS,CAACC,UAAU;YAE/B,IAAI,OAAOC,OAAO,YAAY;gBAC5B,MAAMC,eAAeP,OAAOvC,UAAU,GAAG+C,IAAI,CAAC5E,QAAQgC,MAAM,IAAI0C;gBAChE,OAAON,OAAOlE,KAAK,CAACmE,MAAMC,YAAY,CAACO,OAAOC;oBAC5CN,SAAS,CAACC,UAAU,GAAG,SAAU1E,GAAQ;wBACvC+E,wBAAAA,KAAO/E;wBACP,OAAO4E,aAAaJ,KAAK,CAAC,IAAI,EAAEC;oBAClC;oBAEA,OAAOrC,GAAGoC,KAAK,CAAC,IAAI,EAAEC;gBACxB;YACF,OAAO;gBACL,OAAOJ,OAAOlE,KAAK,CAACmE,MAAMC,YAAY,IAAMnC,GAAGoC,KAAK,CAAC,IAAI,EAAEC;YAC7D;QACF;IACF;IAIOO,UAAU,GAAGrC,IAAgB,EAAQ;QAC1C,MAAM,CAACC,MAAMG,QAAQ,GAA4CJ;QAEjE,MAAMS,cAAc,IAAI,CAACb,cAAc,CACrCQ,CAAAA,2BAAAA,QAASM,UAAU,KAAI,IAAI,CAACtB,kBAAkB;QAEhD,OAAO,IAAI,CAACH,iBAAiB,GAAGoD,SAAS,CAACpC,MAAMG,SAASK;IAC3D;IAEQb,eAAec,UAAiB,EAAE;QACxC,MAAMD,cAAcC,aAChBlD,MAAM8E,OAAO,CAAChF,QAAQgC,MAAM,IAAIoB,cAChC6B;QAEJ,OAAO9B;IACT;IAEO+B,wBAAwB;QAC7B,MAAM3B,SAASvD,QAAQgC,MAAM,GAAGmD,QAAQ,CAAC7D;QACzC,OAAOF,wBAAwBgE,GAAG,CAAC7B;IACrC;AACF;AAEA,MAAM3B,YAAY,AAAC,CAAA;IACjB,MAAMwC,SAAS,IAAI1C;IAEnB,OAAO,IAAM0C;AACf,CAAA;AAEA,SAASxC,SAAS,EAAEzB,cAAc,EAAEC,QAAQ,GAAE"}