/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
  },
  webpack: (config, { isServer }) => {
    // 处理Live2D和其他二进制文件
    config.module.rules.push({
      test: /\.(wasm|bin|dat)$/,
      type: 'asset/resource',
    });

    // 处理音频文件
    config.module.rules.push({
      test: /\.(mp3|wav|ogg|m4a)$/,
      type: 'asset/resource',
    });

    // 处理模型文件
    config.module.rules.push({
      test: /\.(model3\.json|moc3|physics3\.json|pose3\.json|exp3\.json|motion3\.json)$/,
      type: 'asset/resource',
    });

    return config;
  },
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Cross-Origin-Embedder-Policy',
            value: 'require-corp',
          },
          {
            key: 'Cross-Origin-Opener-Policy',
            value: 'same-origin',
          },
        ],
      },
    ];
  },
};

module.exports = nextConfig;
