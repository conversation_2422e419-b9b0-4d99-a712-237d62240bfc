// 表情同步管理器
import { FaceData } from '@/types'

/**
 * 表情映射配置
 */
export interface ExpressionMapping {
  faceExpression: string
  live2dExpression: string
  threshold: number
  weight: number
}

/**
 * 表情同步事件
 */
export interface ExpressionSyncEvents {
  onExpressionChanged?: (expression: string, weight: number) => void
  onEmotionDetected?: (emotion: string, confidence: number) => void
  onParameterUpdated?: (parameterId: string, value: number) => void
}

/**
 * 表情同步配置
 */
export interface ExpressionSyncConfig {
  enableAutoSync: boolean
  sensitivity: number
  smoothing: number
  expressionMappings: ExpressionMapping[]
  parameterMappings: { [key: string]: string }
}

/**
 * 表情同步管理器
 */
export class ExpressionSyncManager {
  private config: ExpressionSyncConfig
  private events: ExpressionSyncEvents
  private currentExpression: string = 'default'
  private currentParameters: Map<string, number> = new Map()
  private smoothingBuffer: Map<string, number[]> = new Map()
  private isActive: boolean = false

  constructor(config: ExpressionSyncConfig, events: ExpressionSyncEvents = {}) {
    this.config = config
    this.events = events
    this.initializeParameters()
  }

  /**
   * 初始化参数
   */
  private initializeParameters(): void {
    // 初始化Live2D参数
    const defaultParameters = [
      'ParamAngleX',      // 头部左右转动
      'ParamAngleY',      // 头部上下点头
      'ParamAngleZ',      // 头部左右倾斜
      'ParamEyeLOpen',    // 左眼开合
      'ParamEyeROpen',    // 右眼开合
      'ParamEyeBallX',    // 眼球左右
      'ParamEyeBallY',    // 眼球上下
      'ParamMouthOpenY',  // 嘴部开合
      'ParamMouthForm',   // 嘴部形状
      'ParamBrowLY',      // 左眉毛
      'ParamBrowRY'       // 右眉毛
    ]

    defaultParameters.forEach(param => {
      this.currentParameters.set(param, 0)
      this.smoothingBuffer.set(param, [])
    })
  }

  /**
   * 开始表情同步
   */
  start(): void {
    this.isActive = true
    console.log('Expression sync started')
  }

  /**
   * 停止表情同步
   */
  stop(): void {
    this.isActive = false
    console.log('Expression sync stopped')
  }

  /**
   * 更新面部数据
   */
  updateFaceData(faceData: FaceData): void {
    if (!this.isActive || !this.config.enableAutoSync) return

    try {
      // 更新头部旋转
      this.updateHeadRotation(faceData.headRotation)

      // 更新眼部参数
      this.updateEyeParameters(faceData.eyeOpenness)

      // 更新嘴部参数
      this.updateMouthParameters(faceData.mouthOpenness)

      // 更新表情
      this.updateExpression(faceData.expressions)

      // 应用平滑处理
      this.applySmoothingAndNotify()

    } catch (error) {
      console.error('Failed to update face data:', error)
    }
  }

  /**
   * 更新头部旋转参数
   */
  private updateHeadRotation(rotation: { x: number; y: number; z: number }): void {
    // 映射头部旋转到Live2D参数
    this.setParameter('ParamAngleX', rotation.y / 30) // Y轴旋转映射到X参数
    this.setParameter('ParamAngleY', -rotation.x / 30) // X轴旋转映射到Y参数
    this.setParameter('ParamAngleZ', rotation.z / 20) // Z轴旋转映射到Z参数
  }

  /**
   * 更新眼部参数
   */
  private updateEyeParameters(eyeOpenness: { left: number; right: number }): void {
    // 映射眼部开合度
    this.setParameter('ParamEyeLOpen', eyeOpenness.left)
    this.setParameter('ParamEyeROpen', eyeOpenness.right)

    // 检测眨眼
    if (eyeOpenness.left < 0.3 && eyeOpenness.right < 0.3) {
      // 双眼眨眼
      this.triggerBlink()
    }
  }

  /**
   * 更新嘴部参数
   */
  private updateMouthParameters(mouthOpenness: number): void {
    // 映射嘴部开合度
    this.setParameter('ParamMouthOpenY', mouthOpenness)

    // 根据开合度调整嘴部形状
    if (mouthOpenness > 0.5) {
      this.setParameter('ParamMouthForm', 0.5) // 张嘴形状
    } else if (mouthOpenness > 0.2) {
      this.setParameter('ParamMouthForm', 0.2) // 微张形状
    } else {
      this.setParameter('ParamMouthForm', 0) // 闭嘴形状
    }
  }

  /**
   * 更新表情
   */
  private updateExpression(expressions: any): void {
    if (!expressions) return

    // 找到最强的表情
    let maxExpression = 'neutral'
    let maxWeight = 0

    for (const [emotion, weight] of Object.entries(expressions)) {
      if (typeof weight === 'number' && weight > maxWeight) {
        maxWeight = weight
        maxExpression = emotion
      }
    }

    // 检查是否超过阈值
    const mapping = this.config.expressionMappings.find(m => 
      m.faceExpression === maxExpression
    )

    if (mapping && maxWeight > mapping.threshold) {
      this.changeExpression(mapping.live2dExpression, maxWeight * mapping.weight)
      this.events.onEmotionDetected?.(maxExpression, maxWeight)
    }
  }

  /**
   * 设置参数值
   */
  private setParameter(parameterId: string, value: number): void {
    // 应用敏感度调整
    const adjustedValue = value * this.config.sensitivity

    // 限制值范围
    const clampedValue = Math.max(-1, Math.min(1, adjustedValue))

    // 添加到平滑缓冲区
    const buffer = this.smoothingBuffer.get(parameterId) || []
    buffer.push(clampedValue)

    // 保持缓冲区大小
    const bufferSize = Math.max(1, Math.floor(this.config.smoothing * 10))
    if (buffer.length > bufferSize) {
      buffer.shift()
    }

    this.smoothingBuffer.set(parameterId, buffer)
  }

  /**
   * 应用平滑处理并通知
   */
  private applySmoothingAndNotify(): void {
    for (const [parameterId, buffer] of this.smoothingBuffer.entries()) {
      if (buffer.length === 0) continue

      // 计算平滑值
      const smoothedValue = buffer.reduce((sum, val) => sum + val, 0) / buffer.length

      // 检查是否有变化
      const currentValue = this.currentParameters.get(parameterId) || 0
      if (Math.abs(smoothedValue - currentValue) > 0.01) {
        this.currentParameters.set(parameterId, smoothedValue)
        this.events.onParameterUpdated?.(parameterId, smoothedValue)
      }
    }
  }

  /**
   * 改变表情
   */
  private changeExpression(expression: string, weight: number = 1.0): void {
    if (this.currentExpression !== expression) {
      this.currentExpression = expression
      this.events.onExpressionChanged?.(expression, weight)
    }
  }

  /**
   * 触发眨眼动画
   */
  private triggerBlink(): void {
    // 临时设置眨眼参数
    this.events.onParameterUpdated?.('ParamEyeLOpen', 0)
    this.events.onParameterUpdated?.('ParamEyeROpen', 0)

    // 短暂延迟后恢复
    setTimeout(() => {
      this.events.onParameterUpdated?.('ParamEyeLOpen', 1)
      this.events.onParameterUpdated?.('ParamEyeROpen', 1)
    }, 150)
  }

  /**
   * 手动设置表情
   */
  setExpression(expression: string, weight: number = 1.0): void {
    this.changeExpression(expression, weight)
  }

  /**
   * 获取当前表情
   */
  getCurrentExpression(): string {
    return this.currentExpression
  }

  /**
   * 获取当前参数值
   */
  getParameter(parameterId: string): number {
    return this.currentParameters.get(parameterId) || 0
  }

  /**
   * 获取所有参数
   */
  getAllParameters(): { [key: string]: number } {
    return Object.fromEntries(this.currentParameters)
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<ExpressionSyncConfig>): void {
    this.config = { ...this.config, ...newConfig }
  }

  /**
   * 重置所有参数
   */
  reset(): void {
    this.currentParameters.forEach((_, key) => {
      this.currentParameters.set(key, 0)
    })
    this.smoothingBuffer.forEach((_, key) => {
      this.smoothingBuffer.set(key, [])
    })
    this.currentExpression = 'default'
  }

  /**
   * 释放资源
   */
  dispose(): void {
    this.stop()
    this.currentParameters.clear()
    this.smoothingBuffer.clear()
  }
}

/**
 * 默认表情映射配置
 */
export const DEFAULT_EXPRESSION_MAPPINGS: ExpressionMapping[] = [
  { faceExpression: 'happy', live2dExpression: 'happy', threshold: 0.6, weight: 1.0 },
  { faceExpression: 'sad', live2dExpression: 'sad', threshold: 0.5, weight: 1.0 },
  { faceExpression: 'surprised', live2dExpression: 'surprised', threshold: 0.7, weight: 1.0 },
  { faceExpression: 'angry', live2dExpression: 'angry', threshold: 0.6, weight: 1.0 },
  { faceExpression: 'neutral', live2dExpression: 'default', threshold: 0.3, weight: 1.0 }
]

/**
 * 默认配置
 */
export const DEFAULT_EXPRESSION_SYNC_CONFIG: ExpressionSyncConfig = {
  enableAutoSync: true,
  sensitivity: 1.0,
  smoothing: 0.3,
  expressionMappings: DEFAULT_EXPRESSION_MAPPINGS,
  parameterMappings: {
    'head_x': 'ParamAngleX',
    'head_y': 'ParamAngleY',
    'head_z': 'ParamAngleZ',
    'eye_left': 'ParamEyeLOpen',
    'eye_right': 'ParamEyeROpen',
    'mouth': 'ParamMouthOpenY'
  }
}

/**
 * 创建表情同步管理器
 */
export const createExpressionSyncManager = (
  config: ExpressionSyncConfig = DEFAULT_EXPRESSION_SYNC_CONFIG,
  events: ExpressionSyncEvents = {}
) => {
  return new ExpressionSyncManager(config, events)
}
