{"version": 3, "sources": ["../../../src/server/typescript/utils.ts"], "names": ["log", "init", "getTs", "getInfo", "getType<PERSON><PERSON>cker", "getSource", "removeStringQuotes", "isPositionInsideNode", "isDefaultFunctionExport", "isInsideApp", "isAppEntryFile", "isPageFile", "getIsClientEntry", "ts", "info", "appDirRegExp", "message", "project", "projectService", "logger", "opts", "projectDir", "getCurrentDirectory", "RegExp", "replace", "languageService", "getProgram", "fileName", "getSourceFile", "str", "position", "node", "start", "getFullStart", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "isFunctionDeclaration", "hasExportKeyword", "hasDefaultKeyword", "modifiers", "modifier", "kind", "SyntaxKind", "ExportKeyword", "DefaultKeyword", "filePath", "test", "path", "basename", "throwOnInvalidDirective", "source", "isClientEntry", "isDirective", "for<PERSON><PERSON><PERSON><PERSON><PERSON>", "isExpressionStatement", "isStringLiteral", "expression", "text", "e", "messageText", "getStart", "length", "getWidth"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;IASgBA,GAAG;eAAHA;;IAKAC,IAAI;eAAJA;;IAaAC,KAAK;eAALA;;IAIAC,OAAO;eAAPA;;IAIAC,cAAc;eAAdA;;IAIAC,SAAS;eAATA;;IAIAC,kBAAkB;eAAlBA;;IAIHC,oBAAoB;eAApBA;;IAKAC,uBAAuB;eAAvBA;;IAyBAC,WAAW;eAAXA;;IAGAC,cAAc;eAAdA;;IAMAC,UAAU;eAAVA;;IAQGC,gBAAgB;eAAhBA;;;6DA9FC;;;;;;AAKjB,IAAIC;AACJ,IAAIC;AACJ,IAAIC;AAEG,SAASf,IAAIgB,OAAe;IACjCF,KAAKG,OAAO,CAACC,cAAc,CAACC,MAAM,CAACL,IAAI,CAACE;AAC1C;AAGO,SAASf,KAAKmB,IAGpB;IACCP,KAAKO,KAAKP,EAAE;IACZC,OAAOM,KAAKN,IAAI;IAChB,MAAMO,aAAaP,KAAKG,OAAO,CAACK,mBAAmB;IACnDP,eAAe,IAAIQ,OACjB,MAAM,AAACF,CAAAA,aAAa,aAAY,EAAGG,OAAO,CAAC,UAAU;IAEvDxB,IAAI,yCAAyCqB;AAC/C;AAEO,SAASnB;IACd,OAAOW;AACT;AAEO,SAASV;IACd,OAAOW;AACT;AAEO,SAASV;QACPU;IAAP,QAAOA,mCAAAA,KAAKW,eAAe,CAACC,UAAU,uBAA/BZ,iCAAmCV,cAAc;AAC1D;AAEO,SAASC,UAAUsB,QAAgB;QACjCb;IAAP,QAAOA,mCAAAA,KAAKW,eAAe,CAACC,UAAU,uBAA/BZ,iCAAmCc,aAAa,CAACD;AAC1D;AAEO,SAASrB,mBAAmBuB,GAAW;IAC5C,OAAOA,IAAIL,OAAO,CAAC,kBAAkB;AACvC;AAEO,MAAMjB,uBAAuB,CAACuB,UAAkBC;IACrD,MAAMC,QAAQD,KAAKE,YAAY;IAC/B,OAAOD,SAASF,YAAYA,YAAYC,KAAKG,YAAY,KAAKF;AAChE;AAEO,MAAMxB,0BAA0B,CACrCuB;IAEA,IAAIlB,GAAGsB,qBAAqB,CAACJ,OAAO;QAClC,IAAIK,mBAAmB;QACvB,IAAIC,oBAAoB;QAExB,IAAIN,KAAKO,SAAS,EAAE;YAClB,KAAK,MAAMC,YAAYR,KAAKO,SAAS,CAAE;gBACrC,IAAIC,SAASC,IAAI,KAAK3B,GAAG4B,UAAU,CAACC,aAAa,EAAE;oBACjDN,mBAAmB;gBACrB,OAAO,IAAIG,SAASC,IAAI,KAAK3B,GAAG4B,UAAU,CAACE,cAAc,EAAE;oBACzDN,oBAAoB;gBACtB;YACF;QACF;QAEA,4BAA4B;QAC5B,IAAID,oBAAoBC,mBAAmB;YACzC,OAAO;QACT;IACF;IACA,OAAO;AACT;AAEO,MAAM5B,cAAc,CAACmC;IAC1B,OAAO7B,aAAa8B,IAAI,CAACD;AAC3B;AACO,MAAMlC,iBAAiB,CAACkC;IAC7B,OACE7B,aAAa8B,IAAI,CAACD,aAClB,uCAAuCC,IAAI,CAACC,aAAI,CAACC,QAAQ,CAACH;AAE9D;AACO,MAAMjC,aAAa,CAACiC;IACzB,OACE7B,aAAa8B,IAAI,CAACD,aAClB,8BAA8BC,IAAI,CAACC,aAAI,CAACC,QAAQ,CAACH;AAErD;AAGO,SAAShC,iBACde,QAAgB,EAChBqB,uBAAiC;IAEjC,MAAMC,SAAS5C,UAAUsB;IACzB,IAAIsB,QAAQ;QACV,IAAIC,gBAAgB;QACpB,IAAIC,cAAc;QAElBtC,GAAGuC,YAAY,CAACH,QAAS,CAAClB;YACxB,IACElB,GAAGwC,qBAAqB,CAACtB,SACzBlB,GAAGyC,eAAe,CAACvB,KAAKwB,UAAU,GAClC;gBACA,IAAIxB,KAAKwB,UAAU,CAACC,IAAI,KAAK,cAAc;oBACzC,IAAIL,aAAa;wBACfD,gBAAgB;oBAClB,OAAO;wBACL,IAAIF,yBAAyB;4BAC3B,MAAMS,IAAI;gCACRC,aACE;gCACF1B,OAAOD,KAAKwB,UAAU,CAACI,QAAQ;gCAC/BC,QAAQ7B,KAAKwB,UAAU,CAACM,QAAQ;4BAClC;4BACA,MAAMJ;wBACR;oBACF;gBACF;YACF,OAAO;gBACLN,cAAc;YAChB;QACF;QAEA,OAAOD;IACT;IACA,OAAO;AACT"}