{"version": 3, "sources": ["../../../src/client/components/static-generation-bailout.ts"], "names": ["DynamicServerError", "staticGenerationAsyncStorage", "StaticGenBailoutError", "Error", "code", "formatErrorMessage", "reason", "opts", "dynamic", "link", "suffix", "staticGenerationBailout", "staticGenerationStore", "getStore", "forceStatic", "dynamicShouldError", "message", "postpone", "revalidate", "isStaticGeneration", "err", "dynamicUsageDescription", "dynamicUsageStack", "stack"], "mappings": "AAEA,SAASA,kBAAkB,QAAQ,yBAAwB;AAC3D,SAASC,4BAA4B,QAAQ,6CAA4C;AAEzF,MAAMC,8BAA8BC;;;aAClCC,OAAO;;AACT;AASA,SAASC,mBAAmBC,MAAc,EAAEC,IAAkB;IAC5D,MAAM,EAAEC,OAAO,EAAEC,IAAI,EAAE,GAAGF,QAAQ,CAAC;IACnC,MAAMG,SAASD,OAAO,AAAC,0BAAuBA,OAAS;IACvD,OAAO,AAAC,SACND,CAAAA,UAAU,AAAC,uBAAqBA,UAAQ,OAAO,EAAC,IACjD,uDAAqDF,SAAO,OAAKI;AACpE;AAEA,OAAO,MAAMC,0BAAmD,CAC9DL;QACA,EAAEE,OAAO,EAAEC,IAAI,EAAE,sBAAG,CAAC;IAErB,MAAMG,wBAAwBX,6BAA6BY,QAAQ;IACnE,IAAI,CAACD,uBAAuB,OAAO;IAEnC,IAAIA,sBAAsBE,WAAW,EAAE;QACrC,OAAO;IACT;IAEA,IAAIF,sBAAsBG,kBAAkB,EAAE;QAC5C,MAAM,IAAIb,sBACRG,mBAAmBC,QAAQ;YAAEG;YAAMD,SAASA,kBAAAA,UAAW;QAAQ;IAEnE;IAEA,MAAMQ,UAAUX,mBAAmBC,QAAQ;QACzCE;QACA,uEAAuE;QACvE,8EAA8E;QAC9EC,MAAM;IACR;IAEA,2DAA2D;IAC3DG,sBAAsBK,QAAQ,oBAA9BL,sBAAsBK,QAAQ,MAA9BL,uBAAiCN;IAEjC,2EAA2E;IAC3E,QAAQ;IACRM,sBAAsBM,UAAU,GAAG;IAEnC,IAAIN,sBAAsBO,kBAAkB,EAAE;QAC5C,MAAMC,MAAM,IAAIpB,mBAAmBgB;QACnCJ,sBAAsBS,uBAAuB,GAAGf;QAChDM,sBAAsBU,iBAAiB,GAAGF,IAAIG,KAAK;QAEnD,MAAMH;IACR;IAEA,OAAO;AACT,EAAC"}