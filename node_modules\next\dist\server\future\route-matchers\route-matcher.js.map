{"version": 3, "sources": ["../../../../src/server/future/route-matchers/route-matcher.ts"], "names": ["RouteMatcher", "constructor", "definition", "isDynamicRoute", "pathname", "dynamic", "getRouteMatcher", "getRouteRegex", "identity", "isDynamic", "undefined", "match", "result", "test", "params"], "mappings": ";;;;+BAcaA;;;eAAAA;;;uBAXkB;8BAIxB;4BACuB;AAMvB,MAAMA;IAUXC,YAA4BC,WAAe;0BAAfA;QAC1B,IAAIC,IAAAA,qBAAc,EAACD,WAAWE,QAAQ,GAAG;YACvC,IAAI,CAACC,OAAO,GAAGC,IAAAA,6BAAe,EAACC,IAAAA,yBAAa,EAACL,WAAWE,QAAQ;QAClE;IACF;IAEA;;;;GAIC,GACD,IAAWI,WAAmB;QAC5B,OAAO,IAAI,CAACN,UAAU,CAACE,QAAQ;IACjC;IAEA,IAAWK,YAAY;QACrB,OAAO,IAAI,CAACJ,OAAO,KAAKK;IAC1B;IAEOC,MAAMP,QAAgB,EAAwB;QACnD,MAAMQ,SAAS,IAAI,CAACC,IAAI,CAACT;QACzB,IAAI,CAACQ,QAAQ,OAAO;QAEpB,OAAO;YAAEV,YAAY,IAAI,CAACA,UAAU;YAAEY,QAAQF,OAAOE,MAAM;QAAC;IAC9D;IAEOD,KAAKT,QAAgB,EAA2B;QACrD,IAAI,IAAI,CAACC,OAAO,EAAE;YAChB,MAAMS,SAAS,IAAI,CAACT,OAAO,CAACD;YAC5B,IAAI,CAACU,QAAQ,OAAO;YAEpB,OAAO;gBAAEA;YAAO;QAClB;QAEA,IAAIV,aAAa,IAAI,CAACF,UAAU,CAACE,QAAQ,EAAE;YACzC,OAAO,CAAC;QACV;QAEA,OAAO;IACT;AACF"}