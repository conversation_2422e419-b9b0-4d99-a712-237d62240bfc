// 信令客户端 (模拟实现)
import { io, Socket } from 'socket.io-client'

/**
 * 信令消息类型
 */
export interface SignalingMessage {
  type: 'offer' | 'answer' | 'ice-candidate' | 'join-room' | 'leave-room' | 'user-joined' | 'user-left'
  data: any
  from?: string
  to?: string
  roomId?: string
}

/**
 * 信令客户端
 */
export class SignalingClient {
  private socket: Socket | null = null
  private roomId: string | null = null
  private userId: string
  private isConnected: boolean = false
  private messageHandlers: Map<string, (message: SignalingMessage) => void> = new Map()

  constructor(userId: string) {
    this.userId = userId
  }

  /**
   * 连接到信令服务器
   */
  async connect(serverUrl: string = 'ws://localhost:3001'): Promise<void> {
    try {
      this.socket = io(serverUrl, {
        transports: ['websocket']
      })

      // 设置事件监听器
      this.setupEventListeners()

      // 等待连接建立
      await new Promise<void>((resolve, reject) => {
        this.socket!.on('connect', () => {
          this.isConnected = true
          console.log('Connected to signaling server')
          resolve()
        })

        this.socket!.on('connect_error', (error) => {
          console.error('Failed to connect to signaling server:', error)
          reject(error)
        })

        // 设置超时
        setTimeout(() => {
          if (!this.isConnected) {
            reject(new Error('Connection timeout'))
          }
        }, 5000)
      })
    } catch (error) {
      console.error('Signaling connection failed:', error)
      // 使用模拟模式
      this.setupMockMode()
    }
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    if (!this.socket) return

    this.socket.on('disconnect', () => {
      this.isConnected = false
      console.log('Disconnected from signaling server')
    })

    this.socket.on('message', (message: SignalingMessage) => {
      console.log('Received signaling message:', message)
      this.handleMessage(message)
    })

    this.socket.on('user-joined', (data: { userId: string, roomId: string }) => {
      console.log('User joined room:', data)
      this.handleMessage({
        type: 'user-joined',
        data,
        roomId: data.roomId
      })
    })

    this.socket.on('user-left', (data: { userId: string, roomId: string }) => {
      console.log('User left room:', data)
      this.handleMessage({
        type: 'user-left',
        data,
        roomId: data.roomId
      })
    })
  }

  /**
   * 设置模拟模式 (用于演示)
   */
  private setupMockMode(): void {
    console.log('Using mock signaling mode')
    this.isConnected = true

    // 模拟信令消息处理
    setTimeout(() => {
      this.handleMessage({
        type: 'user-joined',
        data: { userId: 'mock-user', roomId: 'demo-room' },
        roomId: 'demo-room'
      })
    }, 1000)
  }

  /**
   * 加入房间
   */
  async joinRoom(roomId: string): Promise<void> {
    if (!this.isConnected) {
      throw new Error('Not connected to signaling server')
    }

    this.roomId = roomId

    if (this.socket) {
      this.socket.emit('join-room', {
        roomId,
        userId: this.userId
      })
    }

    console.log(`Joined room: ${roomId}`)
  }

  /**
   * 离开房间
   */
  async leaveRoom(): Promise<void> {
    if (!this.roomId) return

    if (this.socket) {
      this.socket.emit('leave-room', {
        roomId: this.roomId,
        userId: this.userId
      })
    }

    console.log(`Left room: ${this.roomId}`)
    this.roomId = null
  }

  /**
   * 发送信令消息
   */
  sendMessage(message: Omit<SignalingMessage, 'from'>): void {
    if (!this.isConnected) {
      console.warn('Cannot send message: not connected')
      return
    }

    const fullMessage: SignalingMessage = {
      ...message,
      from: this.userId,
      roomId: this.roomId || undefined
    }

    if (this.socket) {
      this.socket.emit('message', fullMessage)
    } else {
      // 模拟模式：直接处理消息
      setTimeout(() => {
        this.handleMockResponse(fullMessage)
      }, 100)
    }

    console.log('Sent signaling message:', fullMessage)
  }

  /**
   * 处理模拟响应
   */
  private handleMockResponse(originalMessage: SignalingMessage): void {
    // 模拟对方的响应
    if (originalMessage.type === 'offer') {
      // 模拟answer响应
      this.handleMessage({
        type: 'answer',
        data: {
          type: 'answer',
          sdp: 'mock-answer-sdp'
        },
        from: 'mock-peer',
        to: this.userId,
        roomId: this.roomId || undefined
      })
    } else if (originalMessage.type === 'ice-candidate') {
      // 模拟ICE候选响应
      this.handleMessage({
        type: 'ice-candidate',
        data: {
          candidate: 'mock-ice-candidate',
          sdpMLineIndex: 0,
          sdpMid: 'video'
        },
        from: 'mock-peer',
        to: this.userId,
        roomId: this.roomId || undefined
      })
    }
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(message: SignalingMessage): void {
    const handler = this.messageHandlers.get(message.type)
    if (handler) {
      handler(message)
    } else {
      console.log('No handler for message type:', message.type)
    }
  }

  /**
   * 注册消息处理器
   */
  onMessage(type: string, handler: (message: SignalingMessage) => void): void {
    this.messageHandlers.set(type, handler)
  }

  /**
   * 移除消息处理器
   */
  offMessage(type: string): void {
    this.messageHandlers.delete(type)
  }

  /**
   * 获取连接状态
   */
  isConnectionActive(): boolean {
    return this.isConnected
  }

  /**
   * 获取当前房间ID
   */
  getCurrentRoomId(): string | null {
    return this.roomId
  }

  /**
   * 获取用户ID
   */
  getUserId(): string {
    return this.userId
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect()
      this.socket = null
    }

    this.isConnected = false
    this.roomId = null
    this.messageHandlers.clear()

    console.log('Signaling client disconnected')
  }

  /**
   * 释放资源
   */
  dispose(): void {
    this.disconnect()
  }
}

/**
 * 创建信令客户端
 */
export const createSignalingClient = (userId: string) => {
  return new SignalingClient(userId)
}

/**
 * 生成随机用户ID
 */
export const generateUserId = (): string => {
  return `user_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * 生成随机房间ID
 */
export const generateRoomId = (): string => {
  return `room_${Math.random().toString(36).substr(2, 9)}`
}
