'use client'

import { useEffect, useState } from 'react'

export default function LoadingScreen() {
  const [progress, setProgress] = useState(0)
  const [loadingText, setLoadingText] = useState('初始化系统...')

  useEffect(() => {
    const steps = [
      { progress: 20, text: '加载Live2D引擎...' },
      { progress: 40, text: '初始化数字人模型...' },
      { progress: 60, text: '配置AI聊天服务...' },
      { progress: 80, text: '准备视频通信...' },
      { progress: 100, text: '启动完成！' }
    ]

    let currentStep = 0
    const interval = setInterval(() => {
      if (currentStep < steps.length) {
        setProgress(steps[currentStep].progress)
        setLoadingText(steps[currentStep].text)
        currentStep++
      } else {
        clearInterval(interval)
      }
    }, 400)

    return () => clearInterval(interval)
  }, [])

  return (
    <div className="fixed inset-0 bg-gradient-to-br from-blue-600 to-purple-700 flex items-center justify-center z-50">
      <div className="text-center text-white">
        {/* Logo区域 */}
        <div className="mb-8">
          <div className="w-24 h-24 mx-auto mb-4 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
            </div>
          </div>
          <h1 className="text-3xl font-bold mb-2">Suziren Digital Avatar</h1>
          <p className="text-blue-100">明星数字人视频聊天工具</p>
        </div>

        {/* 进度条 */}
        <div className="w-80 mx-auto mb-6">
          <div className="bg-white/20 rounded-full h-2 mb-4 backdrop-blur-sm">
            <div 
              className="bg-gradient-to-r from-blue-400 to-purple-400 h-2 rounded-full transition-all duration-500 ease-out"
              style={{ width: `${progress}%` }}
            />
          </div>
          <p className="text-blue-100 text-sm">{loadingText}</p>
        </div>

        {/* 加载动画 */}
        <div className="flex justify-center space-x-2">
          {[0, 1, 2].map((i) => (
            <div
              key={i}
              className="w-3 h-3 bg-white/60 rounded-full animate-bounce"
              style={{ animationDelay: `${i * 0.2}s` }}
            />
          ))}
        </div>

        {/* 版本信息 */}
        <div className="mt-8 text-xs text-blue-200">
          <p>版本 1.0.0 | 基于 Live2D & WebRTC 技术</p>
        </div>
      </div>
    </div>
  )
}
