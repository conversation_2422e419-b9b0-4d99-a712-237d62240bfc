import { NextRequest, NextResponse } from 'next/server'
import OpenAI from 'openai'

// 初始化OpenAI客户端
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY || process.env.NEXT_PUBLIC_OPENAI_API_KEY,
})

// 系统提示词
const SYSTEM_PROMPT = `你是一个友好的数字人助手，名叫小铃。你的特点是：

1. 性格活泼开朗，喜欢与用户互动
2. 会根据对话内容表现出相应的情感
3. 回答简洁明了，通常控制在50-100字以内
4. 偶尔会使用一些可爱的表情符号，但不要过度使用
5. 能够理解用户的情感并给予适当的回应
6. 作为数字人，你可以表现出各种表情和动作

请用中文回答，保持友好和专业的态度。如果用户询问你的身份，请说你是一个Live2D数字人助手。`

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { message, conversationHistory = [] } = body

    if (!message) {
      return NextResponse.json(
        { error: 'Message is required' },
        { status: 400 }
      )
    }

    // 检查API密钥
    if (!openai.apiKey) {
      // 如果没有API密钥，返回模拟响应
      const mockResponse = generateMockResponse(message)
      return NextResponse.json({
        response: mockResponse,
        emotion: analyzeEmotion(mockResponse),
        timestamp: new Date().toISOString()
      })
    }

    // 构建对话历史
    const messages = [
      { role: 'system', content: SYSTEM_PROMPT },
      ...conversationHistory.slice(-10).map((msg: any) => ({
        role: msg.type === 'user' ? 'user' : 'assistant',
        content: msg.content
      })),
      { role: 'user', content: message }
    ]

    // 调用OpenAI API
    const completion = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: messages as any,
      max_tokens: 150,
      temperature: 0.7,
      presence_penalty: 0.1,
      frequency_penalty: 0.1
    })

    const response = completion.choices[0]?.message?.content || '抱歉，我现在无法回答。'
    const emotion = analyzeEmotion(response)

    return NextResponse.json({
      response,
      emotion,
      timestamp: new Date().toISOString(),
      usage: completion.usage
    })

  } catch (error) {
    console.error('Chat API error:', error)
    
    // 如果API调用失败，返回友好的错误响应
    const fallbackResponse = '抱歉，我现在有点忙，请稍后再试试吧~ 😅'
    
    return NextResponse.json({
      response: fallbackResponse,
      emotion: 'neutral',
      timestamp: new Date().toISOString(),
      error: 'API_ERROR'
    })
  }
}

/**
 * 生成模拟响应 (当没有API密钥时使用)
 */
function generateMockResponse(message: string): string {
  const responses = {
    greeting: [
      '你好！很高兴见到你！😊',
      'Hi！今天过得怎么样呀？',
      '嗨～我是小铃，你的数字人助手！'
    ],
    question: [
      '这是个很有趣的问题呢！让我想想...',
      '嗯，关于这个问题，我觉得...',
      '你问得很好！我来帮你分析一下～'
    ],
    compliment: [
      '谢谢夸奖！你也很棒呢！✨',
      '哇，真是太开心了！😄',
      '你这么说我都不好意思了～'
    ],
    goodbye: [
      '再见！期待下次和你聊天！👋',
      'Bye bye～记得想我哦！',
      '下次见！要开心哦！😊'
    ],
    default: [
      '我明白你的意思！这确实很有意思呢～',
      '嗯嗯，我觉得你说得对！',
      '哇，你的想法很独特呢！',
      '这让我学到了新东西，谢谢你！',
      '我们聊得真开心！还想聊什么呢？'
    ]
  }

  const lowerMessage = message.toLowerCase()
  
  if (lowerMessage.includes('你好') || lowerMessage.includes('hi') || lowerMessage.includes('hello')) {
    return getRandomResponse(responses.greeting)
  } else if (lowerMessage.includes('?') || lowerMessage.includes('？') || lowerMessage.includes('什么') || lowerMessage.includes('怎么')) {
    return getRandomResponse(responses.question)
  } else if (lowerMessage.includes('棒') || lowerMessage.includes('好') || lowerMessage.includes('厉害') || lowerMessage.includes('赞')) {
    return getRandomResponse(responses.compliment)
  } else if (lowerMessage.includes('再见') || lowerMessage.includes('bye') || lowerMessage.includes('拜拜')) {
    return getRandomResponse(responses.goodbye)
  } else {
    return getRandomResponse(responses.default)
  }
}

/**
 * 获取随机响应
 */
function getRandomResponse(responses: string[]): string {
  return responses[Math.floor(Math.random() * responses.length)]
}

/**
 * 分析情感
 */
function analyzeEmotion(text: string): string {
  const emotionKeywords = {
    happy: ['开心', '高兴', '快乐', '哈哈', '笑', '好棒', '太好了', '😊', '😄', '😁', '✨'],
    sad: ['难过', '伤心', '哭', '痛苦', '失望', '😢', '😭', '😞'],
    surprised: ['惊讶', '震惊', '不敢相信', '天哪', '哇', '😮', '😲', '😱'],
    excited: ['兴奋', '激动', '太棒了', 'amazing', '🎉', '🔥'],
    neutral: ['好的', '知道了', '明白', '嗯', '是的', '不是']
  }

  let maxScore = 0
  let detectedEmotion = 'neutral'

  for (const [emotion, keywords] of Object.entries(emotionKeywords)) {
    let score = 0
    keywords.forEach(keyword => {
      if (text.includes(keyword)) {
        score += 1
      }
    })

    if (score > maxScore) {
      maxScore = score
      detectedEmotion = emotion
    }
  }

  return detectedEmotion
}

// 支持GET请求用于健康检查
export async function GET() {
  return NextResponse.json({
    status: 'ok',
    message: 'Chat API is running',
    timestamp: new Date().toISOString()
  })
}
